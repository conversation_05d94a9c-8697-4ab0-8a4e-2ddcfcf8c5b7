name: deploy website

on:
  workflow_dispatch:
    inputs:
      admin_github_token:
        description: '管理员密钥'
        required: true
        type: string
      repository_account:
        description: '仓库'
        required: true
        type: string
        default: 'isxcode'
      branch_number:
        description: '分支'
        required: true
        type: string
        default: 'main'

env:
  ADMIN_GITHUB_TOKEN: ${{ inputs.admin_github_token }}

jobs:

  build:
    runs-on: ubuntu-latest

    permissions:
      contents: read
      packages: write
      id-token: write

    strategy:
      fail-fast: false
      matrix:
        node:
          - '18'

    steps:

      - name: Set timezone to Asia/Shanghai
        run: |
          sudo timedatectl set-timezone Asia/Shanghai
          date

      - name: Checkout spark-yun
        uses: actions/checkout@v4
        with:
          token: ${{ env.ADMIN_GITHUB_TOKEN }}
          repository: ${{ inputs.repository_account }}/spark-yun
          ref: ${{ inputs.branch_number }}

      - name: Install pnpm
        uses: pnpm/action-setup@v4
        with:
          version: 9.0.6
          run_install: false

      - name: Install Node.js
        uses: actions/setup-node@v4
        with:
          node-version: 18

      - name: Get pnpm store directory
        shell: bash
        run: |
          echo "STORE_PATH=$(pnpm store path --silent)" >> $GITHUB_ENV

      - name: Setup pnpm cache
        uses: actions/cache@v4
        with:
          path: ${{ env.STORE_PATH }}
          key: ${{ runner.os }}-pnpm-store-${{ hashFiles('**/pnpm-lock.yaml') }}
          restore-keys: |
            ${{ runner.os }}-pnpm-store-

      - name: Build with Nodejs
        run: |
          cd docs
          pnpm install --force
          pnpm run build

      - name: Tar dist
        run: |
          cd /home/<USER>/work/spark-yun/spark-yun/docs
          tar -czf website.tar.gz ./.output
          mv website.tar.gz /home/<USER>/work/spark-yun/spark-yun/

      - name: Copy website via ssh password
        uses: appleboy/scp-action@v0.1.7
        with:
          host: ${{ secrets.HOST }}
          username: ${{ secrets.USERNAME }}
          password: ${{ secrets.PASSWORD }}
          port: 22
          source: "website.tar.gz"
          target: /tmp/

      - name: Deploy website
        uses: appleboy/ssh-action@v1.0.3
        with:
          host: ${{ secrets.HOST }}
          username: ${{ secrets.USERNAME }}
          password: ${{ secrets.PASSWORD }}
          script_stop: false
          timeout: 1800s
          script: |
            pm2 delete all
            rm -rf /opt/.output
            tar -vzxf /tmp/website.tar.gz -C /opt/
            rm -rf /tmp/website.tar.gz
            pm2 start /opt/.output/server/index.mjs