export default [
    {
        formTypeCode: 'LABEL',
        formInstanceName: 'FormLabel',
        formConfigValueCode: 'label'
    },
    {
        formTypeCode: 'WIDTH',
        formInstanceName: 'FormWidth',
        formConfigValueCode: 'width'
    },
    {
        formTypeCode: 'PLACEHOLDER',
        formInstanceName: 'FormPlaceholder',
        formConfigValueCode: 'placeholder'
    },
    {
        formTypeCode: 'DISABLED',
        formInstanceName: 'FormDisabled',
        formConfigValueCode: 'disabled'
    },
    {
        formTypeCode: 'REQUIRED',
        formInstanceName: 'FormRequired',
        formConfigValueCode: 'required'
    },
    {
        formTypeCode: 'MAXLENGTH',
        formInstanceName: 'FormMaxlength',
        formConfigValueCode: 'maxlength'
    },
    // 默认值
    {
        formTypeCode: 'DEFAULTVALUE',
        formInstanceName: 'FormDefaultValue',
        formConfigValueCode: 'defaultValue'
    },
    // 默认值-数字
    {
        formTypeCode: 'DEFAULTVALUE_NUMBER',
        formInstanceName: 'FormDefaultNumber',
        formConfigValueCode: 'defaultValue'
    },
    // 默认值-日期
    {
        formTypeCode: 'DEFAULTVALUE_DATE',
        formInstanceName: 'FormDefaultDate',
        formConfigValueCode: 'defaultValue'
    },
    // 默认值-多选
    {
        formTypeCode: 'DEFAULTVALUE_MULTIPLE',
        formInstanceName: 'FormDefaultMultiple',
        formConfigValueCode: 'defaultValue'
    },
    {
        formTypeCode: 'COLOR_PICKER',
        formInstanceName: 'FormColorPicker',
        formConfigValueCode: 'colorPicker'
    },
    // sql字段绑定
    {
        formTypeCode: 'CODE_SELECT',
        formInstanceName: 'FormCodeSelect',
        formConfigValueCode: 'formValueCode'
    },
    // 是否设为表头
    {
        formTypeCode: 'LIST_COLUMN',
        formInstanceName: 'FormListColumn',
        formConfigValueCode: 'isColumn'
    },
    // 日期类型
    {
        formTypeCode: 'DATE_TYPE',
        formInstanceName: 'FormDateType',
        formConfigValueCode: 'dateType'
    },
    // 下拉、单选、多选 数据字典
    {
        formTypeCode: 'OPTIONS',
        formInstanceName: 'FormOptions',
        formConfigValueCode: 'options'
    },
    // 下拉-是否开启多选
    {
        formTypeCode: 'MULTIPLE',
        formInstanceName: 'FormSelectMultiple',
        formConfigValueCode: 'multiple'
    },
    // 颜色选择
    {
        formTypeCode: 'COLOR_PICKER',
        formInstanceName: 'FormColorPicker',
        formConfigValueCode: 'colorPicker'
    },
    // 开关组件-开关对应文字描述
    {
        formTypeCode: 'SWITCH_INFO',
        formInstanceName: 'FormSwitchInfo',
        formConfigValueCode: 'switchInfo'
    },
    // 数值精度
    {
        formTypeCode: 'PRECISION',
        formInstanceName: 'FormPrecision',
        formConfigValueCode: 'precision'
    },
    // 数值精度
    {
        formTypeCode: 'PRIMARY_COLUMN',
        formInstanceName: 'FormIsPrimaryColumn',
        formConfigValueCode: 'isPrimaryColumn'
    }
]