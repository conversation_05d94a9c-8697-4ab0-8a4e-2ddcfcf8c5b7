{"home": "Accueil", "zhi_qing_yun": "Zhiqing Cloud", "enterprise_data_platform": "Plateforme de calcul Big Data légère", "quick_start": "Démarrage rapide", "experience_now": "Essayer maintenant", "video_introduction": "Introduction vidéo", "copy_success": "<PERSON><PERSON> r<PERSON>", "choose_light_cloud": "Choisir <PERSON>", "related_technologies": "Technologies associées", "data_drives_value": "Les données créent de la valeur, le Big Data crée une grande valeur", "data_drives_value_description": "L'IA est arrivée, Zhiqing Cloud s'associe à vous pour réaliser un centre de données d'entreprise", "light_cloud_description": "Zhiqing Cloud est une plateforme de calcul Big Data ultra-légère et de niveau entreprise. Déploiement en un clic, prêt à l'emploi. Peut rapidement réaliser le calcul Big Data, la collecte de données, le nettoyage de données, la sécurité des données, la qualité des données, la gestion des données, l'ouverture d'interfaces de données et d'autres fonctions, aidant les entreprises à construire un centre Big Data.", "light_cloud_description_mobile": "Zhiqing Cloud est une plateforme de calcul Big Data ultra-légère et de niveau entreprise. Déploiement en un clic, prêt à l'emploi. Peut rapidement réaliser le calcul Big Data, la collecte de données, le nettoyage de données, la sécurité des données, la qualité des données, la gestion des données, l'ouverture d'interfaces de données et d'autres fonctions, aidant les entreprises à construire un centre Big Data.", "coding_capability": "Surveillance panoramique de la plateforme", "job_types_supported": "Compatible avec les moteurs de planification de ressources mainstream tels que Hadoop, CDH, Kubernetes, Spark, etc., réalisant une surveillance unifiée des ressources et du système.", "job_orchestration": "<PERSON>lugin <PERSON>, pas seulement SQL", "job_support": "Prend en charge de nombreux types de tâches, y compris les tâches personnalisées, les tâches Spark, les scripts Python, la synchronisation de données, les scripts Bash, l'importation Excel, les appels d'interface, les tâches SQL, les scripts Curl, etc.", "real_work": "Planification flexible, glisser-déposer dans tous les sens", "real_work_description": "Prend en charge toutes les commandes du cycle de vie des tâches, y compris l'exécution, l'arrê<PERSON>, l'interruption, la mise hors ligne, la publication, la re-exécution complète, la re-exécution en aval, la re-exécution actuelle, l'appel externe, la transmission de résultats, etc.", "multi_platform_deployment": "Carte de métadonnées, claire en un coup d'œil", "multi_platform_description": "Fournit une fonction de collecte de métadonnées, aidant les utilisateurs à rechercher rapidement la distribution et la structure des données, prenant en charge l'accès à plusieurs sources de données et l'affichage de visualisation multidimensionnelle.", "data_view": "Grand écran intelligent, visualisation Big Data", "data_view_description": "Technologie puissante de traitement et de visualisation des données, transformant les données complexes en graphiques intuitifs, prenant en charge la surveillance et l'analyse en temps réel, aidant à la prise de décision d'entreprise, s'adaptant de manière flexible aux besoins de l'industrie.", "opensource_value": "Aider les entreprises à utiliser les capacités Big Data", "free_trial": "<PERSON><PERSON><PERSON> gratuit", "zhi_yao_shu_ju": "Zhiyao Data", "build_enterprise_open_source_software": "Plateforme de calcul Big Data légère"}