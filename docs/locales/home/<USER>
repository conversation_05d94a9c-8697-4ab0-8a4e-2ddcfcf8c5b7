{"home": "Startseite", "zhi_qing_yun": "Zhiqing Cloud", "enterprise_data_platform": "Leichtgewichtige Big Data Computing-Plattform", "quick_start": "Schnellstart", "experience_now": "Jetzt erleben", "video_introduction": "Video-Einführung", "copy_success": "Erfolgreich k<PERSON>", "choose_light_cloud": "Zhiqing Cloud wählen", "related_technologies": "Verwandte Technologien", "data_drives_value": "<PERSON><PERSON> schaffe<PERSON>, Big Data schafft großen Wert", "data_drives_value_description": "KI ist da, Zhiqing Cloud arbeitet mit Ihnen zusammen, um ein Unternehmens-Datenzentrum zu realisieren", "light_cloud_description": "Zhiqing Cloud ist eine ultraleichte, unternehmenstaugliche Big Data Computing-Plattform. Ein-Klick-Bereitstellung, sofort einsatzbereit. Kann schnell Big Data Computing, Datensammlung, Datenbereinigung, Datensicherheit, Datenqualität, Datenmanagement, Dateninterface-Öffnung und andere Funktionen realisieren und Unternehmen beim Aufbau eines Big Data-Zentrums unterstützen.", "light_cloud_description_mobile": "Zhiqing Cloud ist eine ultraleichte, unternehmenstaugliche Big Data Computing-Plattform. Ein-Klick-Bereitstellung, sofort einsatzbereit. Kann schnell Big Data Computing, Datensammlung, Datenbereinigung, Datensicherheit, Datenqualität, Datenmanagement, Dateninterface-Öffnung und andere Funktionen realisieren und Unternehmen beim Aufbau eines Big Data-Zentrums unterstützen.", "coding_capability": "Panorama-Plattform-Überwachung", "job_types_supported": "Kompatibel mit Mainstream-Ressourcen-Scheduling-Engines wie Hadoop, CDH, Kubernetes, Spark usw., um eine einheitliche Überwachung von Ressourcen und Systemen zu realisieren.", "job_orchestration": "Benutzerdefinierte Plugins, nicht nur SQL", "job_support": "Unterstützt viele <PERSON>-<PERSON>, e<PERSON><PERSON><PERSON><PERSON>lich benutzerdefinierte Jobs, Spark-Tasks, Python-Skripte, Datensynchronisation, Bash-Skripte, Excel-Import, Interface-Aufrufe, SQL-Jobs, Curl-Skripte usw.", "real_work": "Flexible Planung, vertikal und horizontal ziehbar", "real_work_description": "Unterstützt alle Job-Lebenszyklus-<PERSON><PERSON><PERSON><PERSON>, e<PERSON>chließlich Ausführung, Stopp, Unterbrechung, Offline, Veröffentlichung, Komplett-Wiederholung, Downstream-Wiederholung, Aktuelle-Wiederholung, Externer Aufruf, Ergebnisübertragung usw.", "multi_platform_deployment": "Metada<PERSON><PERSON><PERSON><PERSON>, auf einen <PERSON> klar", "multi_platform_description": "Bietet Metadaten-Sammelfunktion, hilft Benutzern bei der schnellen Suche nach Datenverteilung und -struktur, unterstützt mehrere Datenquellen-Zugang und mehrdimensionale Visualisierungsanzeige.", "data_view": "Intelligenter große<PERSON> Bildschirm, Big Data-Visualisierung", "data_view_description": "Leistungsstarke Datenverarbeitung und Visualisierungstechnologie, wandelt komplexe Daten in intuitive Diagramme um, unterstützt Echtzeitüberwachung und -analyse, hilft bei Unternehmensentscheidungen, passt sich flexibel an Branchenanforderungen an.", "opensource_value": "Unternehmen bei der Nutzung von Big Data-Fähigkeiten unterstützen", "free_trial": "Kostenlose Testversion", "zhi_yao_shu_ju": "Zhiyao Data", "build_enterprise_open_source_software": "Leichtgewichtige Big Data Computing-Plattform"}