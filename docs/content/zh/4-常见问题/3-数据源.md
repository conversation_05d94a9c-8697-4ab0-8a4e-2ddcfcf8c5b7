---
title: "数据源"
---

#### 问题1: encrypt" property is set to "true" and "trustServerCertificate" property is set to "false" 

> SqlServer连接异常

```log
测试连接失败："encrypt" property is set to "true" and "trustServerCertificate" property is set to "false" but the driver could not establish a secure connection to SQL Server by using Secure Sockets Layer (SSL) encryption: Error: PKIX path building failed: sun.security.provider.certpath.SunCertPathBuilderException: unable to find valid certification path to requested target. ClientConnectionId:a68edd3e-1274-4f92-b695-8bd3dba0e358
```

**解决方案**

```log
JdbcUrl中添加`trustServerCertificate=true`
例如：***************************************************************************
```