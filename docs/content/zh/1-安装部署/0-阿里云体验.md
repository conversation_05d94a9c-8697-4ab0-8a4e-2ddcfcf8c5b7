---
title: "阿里云体验"
---

## 使用阿里云抢占服务器体验至轻云平台

#### 1. 抢占阿里云服务器

阿里云链接:  https://ecs.console.aliyun.com/

> 选择`抢占式实例`，4核心8GB任意选择

![20241101175851](https://img.isxcode.com/picgo/20241101175851.png)

> 选择`Centos 7.9 64位`的系统版本

![20241101175920](https://img.isxcode.com/picgo/20241101175920.png)

> 注意！！安全组需要开放端口号: `8080`(访问端口必须开放，至轻云默认端口号8080)

![20241101175947](https://img.isxcode.com/picgo/20241101175947.png)

> 获取服务器的`内网ip`和`外网ip`

![20241101180120](https://img.isxcode.com/picgo/20241101180120.png)

系统信息如下

> 系统: Centos 7.9
> 资源: 4核8GB
> 外网ip: *************
> 内网ip: *************
> 账号: root
> 密码: Zhiqingyun123..

#### 2. 登录服务器

```bash
ssh root@*************
```

![20241101180227](https://img.isxcode.com/picgo/20241101180227.png)

#### 3. 安装java环境(在线安装/推荐)

> Ubuntu系统，执行命令如下:
> sudo apt update
> sudo apt install openjdk-8-jdk openjdk-8-jre -y

```bash
sudo yum install java-1.8.0-openjdk-devel java-1.8.0-openjdk -y 
```

![20241101180421](https://img.isxcode.com/picgo/20241101180421.png)

**安装java环境(离线安装/可选)**

```bash
cd /tmp
nohup wget https://openfly.oss-cn-shanghai.aliyuncs.com/java/zulu8.78.0.19-ca-jdk8.0.412-linux_x64.tar.gz >> download_jdk.log 2>&1 &
tail -f download_jdk.log
```

> 注意！！！一定要在~/.bashrc中配置JAVA_HOME环境变量

```bash
tar -vzxf /tmp/zulu8.78.0.19-ca-jdk8.0.412-linux_x64.tar.gz -C /opt
ln -s /opt/zulu8.78.0.19-ca-jdk8.0.412-linux_x64 /opt/java
tee -a ~/.bashrc <<-'EOF'
export JAVA_HOME=/opt/java
export PATH=$PATH:$JAVA_HOME/bin
EOF
source ~/.bashrc
java -version
```

#### 4. 下载至轻云安装包

> 耐心等待下载，大约1GB大小的安装包

```bash
cd /tmp
nohup wget https://isxcode.oss-cn-shanghai.aliyuncs.com/zhiqingyun/zhiqingyun.tar.gz >> download_zhiqingyun.log 2>&1 &
tail -f download_zhiqingyun.log
```

![20241101181047](https://img.isxcode.com/picgo/20241101181047.png)

#### 5. 解压安装包

```bash
cd /tmp
tar -vzxf zhiqingyun.tar.gz
```

![20241101181114](https://img.isxcode.com/picgo/20241101181114.png)

#### 6. 启动至轻云

```bash
cd /tmp/zhiqingyun/bin
bash start.sh
```

![20241101181156](https://img.isxcode.com/picgo/20241101181156.png)

#### 7. 检测服务是否启动

访问健康检测接口: http://*************:8080/tools/open/health

![20241101181317](https://img.isxcode.com/picgo/20241101181317.png)

#### 8. 访问至轻云平台

> 1M的带宽，首次加载，大约40s

- 访问接口: http://*************:8080 
- 后台管理员账号: `admin` 
- 默认密码: `admin123`

![20241101181443](https://img.isxcode.com/picgo/20241101181443.png)

#### 9. 创建用户和租户

- 创建用户`zhiqingyun` 
- 创建租户`体验租户`

![20241219160103](https://img.isxcode.com/picgo/20241219160103.png)

#### 10. 上传许可证（可选）

> 仅使用基础功能，不需要上传许可证
> 可免费获取体验许可证，在官网的最下面  

官网链接: https://zhiqingyun.isxcode.com/

![20241101181637](https://img.isxcode.com/picgo/20241101181637.png)

![20241101181601](https://img.isxcode.com/picgo/20241101181601.png)

#### 11. 添加计算集群

> 退出后台管理界面，使用`zhiqingyun`账号登录

![20241101181813](https://img.isxcode.com/picgo/20241101181813.png)

![20241101181829](https://img.isxcode.com/picgo/20241101181829.png)

![20241101181846](https://img.isxcode.com/picgo/20241101181846.png)

>  选择`StandAlone`的集群类型

![20241101181911](https://img.isxcode.com/picgo/20241101181911.png)

> 点击集群名称，添加服务器节点

![20241101181923](https://img.isxcode.com/picgo/20241101181923.png)

> 推荐使用内网ip
> host: *************
> 用户名:  root
> 密码:  Zhiqingyun123..
> 默认安装Spark:  `打勾`

![20241101182039](https://img.isxcode.com/picgo/20241101182039.png)

![20241101182056](https://img.isxcode.com/picgo/20241101182056.png)

![20241101182118](https://img.isxcode.com/picgo/20241101182118.png)

![20241101182151](https://img.isxcode.com/picgo/20241101182151.png)

#### 12. 添加H2数据源

> 类型: `H2`
> 链接信息: `jdbc:h2:file:~/.zhiqingyun/h2/data_test;AUTO_SERVER=TRUE`
> 用户名: `root`
> 密码: `zhiqingyun123`

![20250428114946](https://img.isxcode.com/picgo/20250428114946.png)

![20250428115037](https://img.isxcode.com/picgo/20250428115037.png)

![20250428115102](https://img.isxcode.com/picgo/20250428115102.png)

#### 13. 新建作业流，新建SparkSql查询作业

![20241101183016](https://img.isxcode.com/picgo/20241101183016.png)

![20241101183042](https://img.isxcode.com/picgo/20241101183042.png)

![20241101183120](https://img.isxcode.com/picgo/20241101183120.png)

![20241101183235](https://img.isxcode.com/picgo/20241101183235.png)

![20241101183248](https://img.isxcode.com/picgo/20241101183248.png)

#### 14. 新建Jdbc执行作业

> 选择h2数据源
> 创建两张表，一张原始表一张结果表，并在原始表中插入一条数据

![20241101183356](https://img.isxcode.com/picgo/20241101183356.png)

```sql
-- 原始表
create table users(
    username varchar(100),
    sex int,
    birth datetime
);

-- 结果表
create table users_result(
    username varchar(100),
    sex int,
    birth varchar(100)
);

-- 结果表中插入一条数据
insert into users values('张三',13,now());
```

![20241101183429](https://img.isxcode.com/picgo/20241101183429.png)

#### 15. 新建Jdbc查询作业

> 选择h2数据源
> 通过jdbc查询作业，查看原始表中的数据。

```sql
select * from users;
```

![20241101183455](https://img.isxcode.com/picgo/20241101183455.png)

![20241101183511](https://img.isxcode.com/picgo/20241101183511.png)

#### 16. 使用自定义函数

> 自定义函数需要先上传编译的jar包到`资源中心`
> 官方自定义函数模版： https://github.com/isxcode/spark-function-template
  
- 演示函数文件下载:
https://isxcode.oss-cn-shanghai.aliyuncs.com/zhiqingyun/downloads/spark-custom-func.jar
- 演示依赖文件下载:
https://isxcode.oss-cn-shanghai.aliyuncs.com/zhiqingyun/downloads/hutool-all-5.8.27.jar

![20241101183821](https://img.isxcode.com/picgo/20241101183821.png)

![20241101183910](https://img.isxcode.com/picgo/20241101183910.png)

![20241101183940](https://img.isxcode.com/picgo/20241101183940.png)

![20241101183951](https://img.isxcode.com/picgo/20241101183951.png)

#### 17. 新建自定义函数

![20241101184033](https://img.isxcode.com/picgo/20241101184033.png)

> 名称: to_chinese_date
> 类型: UDF
> 类名: com.isxcode.star.udf.Func
> 返回类型: string
> 备注: 将英文时间格式转成中文时间

![20241101184138](https://img.isxcode.com/picgo/20241101184138.png)

![20241101184151](https://img.isxcode.com/picgo/20241101184151.png)

#### 18. 新建数据同步作业

> 使用自定义函数，将原始表中的英文日期格式改成中文格式，并同步到结果表中。

![20250428124718](https://img.isxcode.com/picgo/20250428124718.png)

> 转换字段 `to_chinese_date(birth)`

![20250312161958](https://img.isxcode.com/picgo/20250312161958.png)

> 添加自定义函数`to_chinese_date`
> 添加函数依赖`hutool-all-5.8.27.jar`

![20250428125020](https://img.isxcode.com/picgo/20250428125020.png)

> 使用jdbc查询作业，查询结果

```sql
select * from users_result;
```

![20250312163313](https://img.isxcode.com/picgo/20250312163313.png)

#### 19. 新建自定义作业

> 计算pi的值
> 官方自定义作业模版：https://github.com/isxcode/spark-job-template

- 官网pi计算文件下载:
https://isxcode.oss-cn-shanghai.aliyuncs.com/zhiqingyun/downloads/spark-examples_2.12-3.4.1.jar

> 上传资源中心，选择`作业`类型

![20241101184740](https://img.isxcode.com/picgo/20241101184740.png)

![20241101184813](https://img.isxcode.com/picgo/20241101184813.png)

> 应用名称: pi-demo
> mainClass: org.apache.spark.examples.SparkPi
> 请求参数: 10

![20241101184903](https://img.isxcode.com/picgo/20241101184903.png)

![20241101184941](https://img.isxcode.com/picgo/20241101184941.png)

> 点击数据返回，查看pi结果

![20250312182142](https://img.isxcode.com/picgo/20250312182142.png)

#### 更多功能请看产品手册