---
title: "插件调试"
---

#### 本地调试自定义插件

前提：创建一个standalone类型的计算集群   
通过控制台关闭计算集群节点服务，不要使用界面重新检测状态，否则提交作业会校验计算集群不可用。

```bash
cd ~/zhiqingyun-agent/bin
bash stop.sh
```

通过idea启动计算集群节点代码

![20241206175513](https://img.isxcode.com/picgo/20241206175513.png)

指定代码处打断点,获取args的值

> spark-yun/spark-yun-agent/src/main/java/com/isxcode/star/agent/run/impl/StandaloneAgentService.java

![20241212183949](https://img.isxcode.com/picgo/20241212183949.png)

通过界面，点击作业运行按钮，触发作业提交

#### 以调试数据同步插件为例

> spark-yun/spark-yun-plugins/spark-data-sync-jdbc-plugin/src/main/java/com/isxcode/star/plugin/dataSync/jdbc/Execute.java

> 将插件中的args变量，提前改成代码断点处获取的args值

```java
public static void main(String[] args) {
        
        args=new String[]{"代码断点处获取的args值"};
}
```

> 将sparkConf中的配置注释掉，改用`spark.master`设置为`local[*]`

```java

public static SparkConf initSparkConf(Map<String, String> sparkConfig) {
    SparkConf conf = new SparkConf();
    
    // 注释SparkConf的代码
    //if (sparkConfig != null) {
    //    for (Map.Entry<String, String> entry : sparkConfig.entrySet()) {
    //        conf.set(entry.getKey(), entry.getValue());
    //    }
    //}
    
    conf.set("spark.master", "local[*]");
    return conf;
}
```

引入缺少的依赖

> 比如数据库的驱动等外部依赖

```bash
vim spark-yun-plugins/spark-data-sync-jdbc-plugin/build.gradle
```

```groovy
implementation group: 'com.microsoft.sqlserver', name: 'mssql-jdbc', version: '12.4.2.jre8'
```

开始调试插件

![20241206175947](https://img.isxcode.com/picgo/20241206175947.png)