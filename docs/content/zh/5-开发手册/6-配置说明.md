---
title: "配置说明"
---

### 配置文件说明

> 修改配置文件`application-local.yml`

```bash
vim spark-yun/spark-yun-backend/spark-yun-main/resources/application-local.yml
```

```yml
# 服务端口号
server:
  port: 8080

spring:

  # 配置后台服务访问管理员账号密码
  security:
    user:
      name: admin
      password: admin123

  # 业务数据库的信息配置，默认使用h2数据库，使用全新的db
  datasource:
    driver-class-name: org.h2.Driver
    url: jdbc:h2:file:~/.zhiqingyun/h2/data;AUTO_SERVER=TRUE
    username: root
    password: root123

# jasypt加密key 
jasypt:
  encryptor:
    password: zhiqingyun
    
# 至轻云应用配置
isx-app:
  resources-path: classpath:resources # 资源文件，例如许可证、驱动、头像等资源路径
  admin-passwd: admin123 # 配置至轻云平台admin管理员的密码，仅第一次执行生效
  use-ssl: false # 是否开启ssl
```

### 配置参数加密说明

> 使用jasypt对配置的账号密码进行加密  
> 通过--jasypt.encryptor.password的方式指定密钥，或者通过配置文件指定

```yml
jasypt:
  encryptor:
    password: zhiqingyun
```

> 启动项目

```bash
./gradlew start
```

> 调用接口获取加密值   
> `/tools/open/jasyptEncrypt?text=${需要加密的参数}`

http://localhost:8080/tools/open/jasyptEncrypt?text=root   
http://localhost:8080/tools/open/jasyptEncrypt?text=root123

> 修改配置文件中加密的账号密码，并重启系统
> 格式如下：  
> ENC( ${加密后的参数} )

```yml
spring:
  datasource:
    driver-class-name: org.h2.Driver
    url: jdbc:h2:file:~/.zhiqingyun/h2/data;AUTO_SERVER=TRUE
    username: ENC(1O7GNz9/IkV3hzgDGsH+n5q4ucoJQSTW24ZAfEBYDlMGVmoMXh+EYi8LnLEjlD6W)
    password: ENC(28GLUi7pLUVqsdaCiWW32mT0q5mae9YJaSgWWiYse7K1ZHnZ2zQ2HEnbivlJzZeQ)
```