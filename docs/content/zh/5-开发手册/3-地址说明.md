---
title: "地址说明"
---

#### 至轻云平台访问地址

> 访问至轻云平台   
> 密码配置参数：`application-local.yml`文件中的`isx-app.admin-passwd`

- 访问地址：http://localhost:8080/ 
- 管理员账号：admin 
- 默认密码：admin123

![20241126205343](https://img.isxcode.com/picgo/20241126205343.png)

#### 内置H2数据库访问地址

> 注意！！！请使用浏览器无痕模式访问  
> 密码配置参数：`application-local.yml`文件中的`spring.security.user.password`

- 地址：http://localhost:8080/h2-console 
- 账号：admin 
- 密码：admin123 

![20241126205454](https://img.isxcode.com/picgo/20241126205454.png)

- url: jdbc:h2:file:~/.zhiqingyun/h2/data;AUTO_SERVER=TRUE 
- username: root 
- password: root123

![20241126205558](https://img.isxcode.com/picgo/20241126205558.png)

![20241126205618](https://img.isxcode.com/picgo/20241126205618.png)

#### Druid数据源监控访问地址

> 注意！！！请使用浏览器无痕模式访问  
> 密码配置参数：`application-local.yml`文件中的`spring.security.user.password`

- 地址：http://localhost:8080/druid/index.html 
- 账号：admin 
- 密码：admin123 

![20241126205743](https://img.isxcode.com/picgo/20241126205743.png)

#### Swagger接口文档访问地址

> 注意！！！请使用浏览器无痕模式访问  
> 密码配置参数：`application-local.yml`文件中的`spring.security.user.password`

- 地址：http://localhost:8080/swagger-ui/index.html 
- 账号：admin 
- 密码：admin123 

![20241126205825](https://img.isxcode.com/picgo/20241126205825.png)

#### 获取系统内部缓存数据地址

> 注意！！！请使用浏览器无痕模式访问  
> 密码配置参数：`application-local.yml`文件中的`spring.security.user.password`

- http://localhost:8080/tools/developer/getCacheList

![20250507112203](https://img.isxcode.com/picgo/20250507112203.png)

#### 获取系统内部指定Key缓存数据地址

> 注意！！！请使用浏览器无痕模式访问  
> 密码配置参数：`application-local.yml`文件中的`spring.security.user.password` 

- http://localhost:8080/tools/developer/getCache?name=user

![20250507112246](https://img.isxcode.com/picgo/20250507112246.png)

#### jasypt加密工具地址

> 注意！！！请使用浏览器无痕模式访问  
> 密码配置参数：`application-local.yml`文件中的`spring.security.user.password` 

- http://localhost:8080/tools/developer/jasyptEncrypt?text=admin123

![20250507113952](https://img.isxcode.com/picgo/20250507113952.png)

#### 配置系统日志等级地址

> 注意！！！请使用浏览器无痕模式访问  
> 密码配置参数：`application-local.yml`文件中的`spring.security.user.password`   
> level参数说明：`trace`, `debug`, `info`, `warn`, `error`, `fatal`, `off`

- http://localhost:8080/tools/developer/setLogLevel?level=debug

![20250507112502](https://img.isxcode.com/picgo/20250507112502.png)

#### 健康检查地址

- http://localhost:8080/tools/open/health

![20250507114622](https://img.isxcode.com/picgo/20250507114622.png)

#### 获取当前版本号地址

- http://localhost:8080/tools/open/version

![20250507114637](https://img.isxcode.com/picgo/20250507114637.png)