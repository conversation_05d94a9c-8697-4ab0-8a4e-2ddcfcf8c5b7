---
title: "前端规范"
---

前端开发准备 
node版本：v18 
Vue3+ts+vite+element-plus 

### 主要目录分类

-  assets：统一样式以及icon收录 
-  components：封装的通用组件 
-  layout：布局组件代码 
-  lib：复杂业务组件，后期可能考虑单独抽离出来 
-  plugins：通用插件目录 
-  services：前端请求函数统一处理，根据模块命名文件 
-  store：vuex缓存目录 
-  validator：通用表单校验 
-  views：页面模块开发，根据模块名命名文件夹 

### 代码开发注意事项

文件命名

- 统一使用中划线命名 
- 例如文件夹component-demo或者component-demo.vue 

vue实例引用

- 统一使用中划线小写模式） 
- 例如<component-demo></component-demo> 

组件开发/使用

- 弹窗以及抽屉组件使用 components 目录下的组件 
- 复用性高的组件抽离到components目录 
- vue文件script标签中统一按 <script lang="ts" setup> 来写 
- vue文件style标签中统一使用scss语法，类名命名根据模块名命名，使用节点树形式，由一个根节点包裹 
- lib中开发的新组件统一使用zqy-组件名形式命名 
- 开发的代码尽量保留注释 

vue内容注意事项
 
- 为了保证vue代码编写有个统一风格，参数按照下面的规则进行分类，方便代码读起来更容易 

```ts
<script lang="ts" setup>
// 顶部统一引入模块插件
import { ref, reactive, defineProps, computed } from 'vue'
import { useRoute } from 'vue-router'

// 第一部分，接口，路由以及store部分声明
interface demoIntetface {

}
const route = useRoute()

// 第二部分，参数声明，ref声明的在上，reactive声明的在下
const keyword = ref('')
const object = reactive({})

// 第三部分，props参数
const props = defineProps<{
  modelConfig: ModalConfig;
}>()

// 第四部分，计算属性&侦听器
const computedParam = computed(() => {
  return ''
})
watch(
  () => props.modelConfig.visible,
  (newVal) => {
    visible.value = newVal
  }
)

// 第五部分，函数/事件，统一使用 function 声明
function fun() {}

// 第六部分，生命周期
onMounted(() => {
})
onUnmounted(() => {
})
</script>
```