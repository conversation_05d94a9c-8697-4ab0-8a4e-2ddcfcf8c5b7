---
title: "资源中心"
---

#### 查询资源中心

> 资源中心是至轻云平台的文件管理中心
> 资源中心包括:作业、函数、依赖、Excel四种文件类型
> 支持资源类型、备注搜索
> 支持文件上传、下载、删除、备注等操作

**资源类型说明**

- **作业**:用于`自定义作业`，管理自定义作业jar包，参考链接: https://github.com/isxcode/spark-job-template 
- **函数**:用于`函数仓库`，管理自定义函数jar包，参考链接: https://github.com/isxcode/spark-function-template 
- **依赖**:用于作业运行中所需的基础依赖jar包，参考链接: https://mvnrepository.com/ 
- **Excel**:用于`Excel同步作业`，Excel文件将导到指定的目标表中

![20240427204154](https://img.isxcode.com/picgo/20240427204154.png)

#### 上传资源

> 点击`上传文件`按钮，选择文件的类型，再将文件拖入上传框中，点击`确定`按钮

![20241220145203](https://img.isxcode.com/picgo/20241220145203.png)

- **类型**:必填，资源类型包括:作业、函数、依赖、Excel  
- **资源**:必填，需要上传的资源文件
- **备注**:非必填 

#### 使用资源文件

> 演示用例：解析上传的json文件
> 新建json文件

```bash
vim test.json
```

> 注意json内容，参考以下格式

```json
{"age": 13,"username": "张三"}
{"age": 14,"username": "李四"}
```

> 上传资源文件，使用`依赖`类型，该类型的资源文件使用都需要添加`.jar`后缀

![20250117164517](https://img.isxcode.com/picgo/20250117164517.png)

> 作业配置中，添加`test.json`依赖

![20250117164627](https://img.isxcode.com/picgo/20250117164627.png)

#### standalone中执行作业

> 资源文件地址：{zhiqingyun-agent-path}/file/{fileId}.jar
> 举例：/Users/<USER>/zhiqingyun-agent/file/sy_1880173934985502720.jar      

**zhiqingyun-agent-path**：默认地址为`~`家目录下，即`/home/<USER>/root`  
**fileId**：资源文件id

```sql
CREATE TEMPORARY VIEW jsonTable
USING org.apache.spark.sql.json
OPTIONS (
  path '/Users/<USER>/zhiqingyun-agent/file/sy_1880173934985502720.jar'
);

SELECT * FROM jsonTable
```

![20250117165329](https://img.isxcode.com/picgo/20250117165329.png)

#### k8s中执行作业

> 资源文件地址：file:///opt/spark/jars/{fileId}.jar
> 举例：file:///opt/spark/jars/sy_1880173934985502720.jar  

**fileId**：资源文件id  

```sql
CREATE TEMPORARY VIEW jsonTable
USING org.apache.spark.sql.json
OPTIONS (
  path 'file:///opt/spark/jars/sy_1880173934985502720.jar'
);

SELECT * FROM jsonTable
```

#### yarn中执行作业

> 资源文件地址：
> 需要提前将文件上传到hdfs中
> /isxcode/sy_1880173934985502720.jar  

**zhiqingyun-agent-path**：默认地址为`~`家目录下，即`/home/<USER>/root`   
**fileId**：资源文件id  

```bash
hadoop fs -put {zhiqingyun-agent-path}/file/{fileId}.jar /isxcode/
hadoop fs -put /home/<USER>/zhiqingyun-agent/file/sy_1880173934985502720.jar /isxcode/
```

```sql
CREATE TEMPORARY VIEW jsonTable
USING org.apache.spark.sql.json
OPTIONS (
  path '/isxcode/sy_1880173934985502720.jar'
);

SELECT * FROM jsonTable
```