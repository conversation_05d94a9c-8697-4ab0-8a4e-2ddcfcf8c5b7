---
title: "数据卡片"
---

## 数据卡片模块

> 通过sql构建可视化图表并提供给数据大屏，构建企业大屏展示

#### 查看卡片

> 支持名称、备注搜索

![20241219175822](https://img.isxcode.com/picgo/20241219175822.png)

#### 新建卡片

> 点击`添加卡片`按钮

![20241219180214](https://img.isxcode.com/picgo/20241219180214.png)

- **名称**：卡片名称
- **图表类型**：目前支持三种基础类型`饼图`、`柱状图`、`折线图`，可定制化开发
- **数据源**：指定执行sql的数据源
- **备注**：非必填

#### 编辑卡片

> 通过编写聚合sql，获取数据
> 举例：select x,y from (table data)
> `刷新数据`按钮，可将图表数据保存到卡片预览中

![20241219175834](https://img.isxcode.com/picgo/20241219175834.png)

#### 预览数据

> 通过点击`预览`按钮，预览sql对应的数据

![20241219175854](https://img.isxcode.com/picgo/20241219175854.png)

#### 多聚合sql

> 点击`加号`
> 支持多sql查询合并结果

![20241219175925](https://img.isxcode.com/picgo/20241219175925.png)

#### 数据刷新

> 点击`图表设置`
> 可编辑卡片标题、刷新间隔
- 卡片标题：大屏中显示的title
- 刷新间隔：大屏中卡片数据刷新的隔间时间，默认60s

![20241219175939](https://img.isxcode.com/picgo/20241219175939.png)

#### 发布卡片

> 只有发布中的卡片，才可以在数据大屏中使用

![20250109115559](https://img.isxcode.com/picgo/20250109115559.png)