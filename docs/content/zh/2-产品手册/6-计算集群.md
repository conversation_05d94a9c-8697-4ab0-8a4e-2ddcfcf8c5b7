---
title: "计算集群"
---

## 计算集群

> 计算集群是平台提供计算能力的核心，类似ai训练需要显卡，大数据计算需要计算集群
> 点击`计算集群`菜单，查看当前租户内创建的集群
> 支持名称、备注搜索

![20241219163607](https://img.isxcode.com/picgo/20241219163607.png)

#### 添加计算集群

> 点击`添加集群`按钮，创建集群

![20240427201919](https://img.isxcode.com/picgo/20240427201919.png)

- **集群名称** : 必填，平台内显示的集群名称 
- **类型** : 必填，支持三种集群类型包括 K8s、Yarn、Standalone 
- **备注** : 非必填

##### K8s

> 全称Kubernetes，基于k8s的集群调度，会将作业提交到k8s中进行计算
> 服务器中需要提供k8s服务和kubectl命令执行权限

##### Yarn

> 基于Hadoop下Yarn的集群调度，会将作业提交到yarn中进行计算
> 服务器中需要提供yarn调度服务和yarn命令执行权限

##### Standalone

> 支持spark单机模式运行，服务器中仅需安装java环境即可，推荐java1.8版本

#### 设置默认集群

> 设置默认集群，会在首页优先显示该集群的监控信息

![20241219170653](https://img.isxcode.com/picgo/20241219170653.png)

![20241219170720](https://img.isxcode.com/picgo/20241219170720.png)

#### 添加集群节点

> 点击`集群名称`，进入集群的节点管理界面
> 点击`添加节点`按钮，可添加服务器节点
> 一个集群中，可添加多台服务器节点

![20240427202438](https://img.isxcode.com/picgo/20240427202438.png)

- **名称** : 必填，节点的名称 
- **Host** : 必填，服务器的ip，推荐使用服务器的内网ip地址
- **用户名** : 必填，服务器中的用户名 
- **验证类型** : 必填，服务器验证的方式，包括密码、令牌  
令牌:ssh的私钥验证  
密码:服务器对应的用户密码   
- **默认安装Spark** :仅在`standalone`类型集群下有该选项，将在服务器中默认安装`spark`服务 
- **备注** :非必填

#### 集群节点操作

点击`更多`按钮，支持以下操作 
- `编辑`: 修改节点信息 
- `停止`: 停止节点的监听 
- `检测`: 检测当前节点状态 
- `安装`: 服务器安装监听服务 
- `卸载`: 服务器卸载监听服务 
- `清理`: 清理监听服务器产生的缓存文件 
- `删除`: 删除集群节点，包括卸载节点、清理缓存步骤

![20240427202646](https://img.isxcode.com/picgo/20240427202646.png)

#### 查看安装进度

> 点击`日志`按钮，可查看监听服务的安装进度
> 安装成功后，节点状态会更新为`运行中`状态，集群状态会更新为`可用`状态
> 注意！！！在安装过程中，无法中止，请耐心等待安装结果再执行其他操作

![20240427202911](https://img.isxcode.com/picgo/20240427202911.png)

#### 刷新当前服务器节点使用情况

> 点击`刷新`按钮，可刷新服务器节点实时信息

![20250116165206](https://img.isxcode.com/picgo/20250116165206.png)