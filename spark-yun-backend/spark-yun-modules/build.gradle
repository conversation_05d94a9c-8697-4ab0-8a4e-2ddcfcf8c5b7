dependencies {

    api(project(':spark-yun-backend:spark-yun-common'))
    api(project(':spark-yun-backend:spark-yun-security'))

    api 'org.springframework.boot:spring-boot-starter-cache'

    runtimeOnly 'com.mysql:mysql-connector-j'

    runtimeOnly 'com.h2database:h2'

    runtimeOnly 'org.postgresql:postgresql'

    implementation 'org.springframework.boot:spring-boot-starter-mail'

    implementation "org.apache.calcite:calcite-core:${CALCITE_VERSION}"

    implementation "org.apache.kafka:kafka-clients:${KAFKA_VERSION}"

    implementation fileTree(dir: rootDir.getAbsolutePath() + '/resources/libs', include: ['prql-java-0.5.2.jar'])

    implementation "org.apache.groovy:groovy-all:${GROOVY_VERSION}"

    implementation "com.aliyun:dysmsapi20170525:${ALI_SMS_VERSION}"

    implementation 'org.apache.poi:poi-ooxml:5.2.5'
    implementation 'org.apache.xmlbeans:xmlbeans:5.2.0'
    implementation 'pull-parser:pull-parser:2.1.10'

    implementation 'commons-dbutils:commons-dbutils:1.7'

    implementation 'org.apache.httpcomponents.client5:httpclient5:5.2.1'
    implementation 'org.apache.httpcomponents.core5:httpcore5:5.2.1'
}