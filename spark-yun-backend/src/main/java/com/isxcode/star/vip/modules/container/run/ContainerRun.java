package com.isxcode.star.vip.modules.container.run;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.isxcode.star.api.agent.constants.AgentType;
import com.isxcode.star.api.agent.constants.AgentUrl;
import com.isxcode.star.api.agent.req.*;
import com.isxcode.star.api.agent.res.ContainerCheckRes;
import com.isxcode.star.api.agent.res.DeployContainerRes;
import com.isxcode.star.api.api.constants.PathConstants;
import com.isxcode.star.api.cluster.constants.ClusterNodeStatus;
import com.isxcode.star.api.cluster.dto.ScpFileEngineNodeDto;
import com.isxcode.star.api.datasource.constants.DatasourceType;
import com.isxcode.star.api.work.constants.WorkLog;
import com.isxcode.star.api.work.constants.WorkType;
import com.isxcode.star.api.work.res.RunWorkRes;
import com.isxcode.star.backend.api.base.exceptions.IsxAppException;
import com.isxcode.star.backend.api.base.pojos.BaseResponse;
import com.isxcode.star.backend.api.base.properties.IsxAppProperties;
import com.isxcode.star.common.utils.aes.AesUtils;
import com.isxcode.star.common.utils.http.HttpUtils;
import com.isxcode.star.modules.cluster.entity.ClusterEntity;
import com.isxcode.star.modules.cluster.entity.ClusterNodeEntity;
import com.isxcode.star.modules.cluster.mapper.ClusterNodeMapper;
import com.isxcode.star.modules.cluster.repository.ClusterNodeRepository;
import com.isxcode.star.modules.cluster.repository.ClusterRepository;
import com.isxcode.star.modules.datasource.entity.DatasourceEntity;
import com.isxcode.star.modules.datasource.service.DatasourceService;
import com.isxcode.star.modules.container.entity.ContainerEntity;
import com.isxcode.star.modules.container.repository.ContainerRepository;
import com.isxcode.star.modules.datasource.source.DataSourceFactory;
import com.isxcode.star.modules.datasource.source.Datasource;
import com.jcraft.jsch.JSchException;
import com.jcraft.jsch.SftpException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.mapstruct.ap.internal.util.Strings;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.web.client.HttpServerErrorException;
import org.springframework.web.client.ResourceAccessException;

import java.io.IOException;
import java.time.LocalDateTime;
import java.util.*;

import static com.isxcode.star.common.utils.ssh.SshUtils.executeCommand;
import static com.isxcode.star.common.utils.ssh.SshUtils.scpText;

@Slf4j
@Service
@RequiredArgsConstructor
public class ContainerRun {

    private final ClusterRepository clusterRepository;

    private final ClusterNodeRepository clusterNodeRepository;

    private final IsxAppProperties isxAppProperties;

    private final DatasourceService datasourceService;

    private final ContainerRepository containerRepository;

    private final ClusterNodeMapper clusterNodeMapper;

    private final AesUtils aesUtils;

    private final DataSourceFactory dataSourceFactory;

    public ContainerEntity updateContainerSubmitLog(ContainerEntity container, StringBuilder logBuilder) {

        container.setSubmitLog(logBuilder.toString());
        return containerRepository.saveAndFlush(container);
    }

    /**
     * 异步部署容器.
     */
    public DeployContainerRes deployContainer(ContainerEntity container) throws RuntimeException {

        // 构建容器提交日志
        StringBuilder submitLog = new StringBuilder();

        // 检查计算集群是否存在
        Optional<ClusterEntity> calculateEngineEntityOptional = clusterRepository.findById(container.getClusterId());
        if (!calculateEngineEntityOptional.isPresent()) {
            throw new IsxAppException(LocalDateTime.now() + WorkLog.ERROR_INFO + "申请资源失败 : 计算引擎不存在  \n");
        }

        // 检测集群中是否有启动节点
        List<ClusterNodeEntity> allEngineNodes = clusterNodeRepository
            .findAllByClusterIdAndStatus(calculateEngineEntityOptional.get().getId(), ClusterNodeStatus.RUNNING);
        if (allEngineNodes.isEmpty()) {
            throw new IsxAppException(LocalDateTime.now() + WorkLog.ERROR_INFO + "申请资源失败 : 集群不存在可用节点，请切换一个集群  \n");
        }

        // 节点选择随机数
        ClusterNodeEntity engineNode = allEngineNodes.get(new Random().nextInt(allEngineNodes.size()));
        submitLog.append(LocalDateTime.now()).append(WorkLog.SUCCESS_INFO).append("申请资源完成，激活节点:【")
            .append(engineNode.getName()).append("】\n");
        container = updateContainerSubmitLog(container, submitLog);

        // 检查hive数据源
        DatasourceEntity datasource = datasourceService.getDatasource(container.getDatasourceId());
        if (!DatasourceType.HIVE.equals(datasource.getDbType())) {
            throw new IsxAppException(LocalDateTime.now() + WorkLog.ERROR_INFO + "检查数据源 : 目前仅支持hive数据库 \n");
        }
        if (Strings.isEmpty(datasource.getMetastoreUris())) {
            throw new IsxAppException(
                LocalDateTime.now() + WorkLog.ERROR_INFO + "检查数据源 : hive数据源未配置hive.metastore.uris \n");
        }
        submitLog.append(LocalDateTime.now()).append(WorkLog.SUCCESS_INFO).append("数据源检测完成\n");
        submitLog.append(LocalDateTime.now()).append(WorkLog.SUCCESS_INFO).append("检测运行环境完成\n");
        container = updateContainerSubmitLog(container, submitLog);

        // 开始构建容器
        submitLog.append(LocalDateTime.now()).append(WorkLog.SUCCESS_INFO).append("开始构建容器\n");
        SubmitWorkReq submitWorkReq = new SubmitWorkReq();

        // 判断用户资源配置方式
        Map<String, String> sparkConfig =
            JSON.parseObject(container.getSparkConfig(), new TypeReference<Map<String, String>>() {}.getType());

        // 配置hive数据源
        sparkConfig.put("hive.metastore.uris", datasource.getMetastoreUris());

        // 开始构造SparkSubmit
        SparkSubmit sparkSubmit =
            SparkSubmit.builder().verbose(true).mainClass("com.isxcode.star.plugin.container.sql.Execute")
                .appResource("spark-container-sql-plugin.jar").conf(genSparkSubmitConfig(sparkConfig)).build();

        // 添加自定义username
        if (Strings.isNotEmpty(datasource.getUsername())) {
            sparkSubmit.getConf().put("qing.hive.username", datasource.getUsername());
        }

        // 开始构造PluginReq
        PluginReq pluginReq = PluginReq.builder().sparkConfig(sparkConfig).limit(200).build();
        Datasource datasourceTmp = dataSourceFactory.getDatasource(datasource.getDbType());
        pluginReq.setDatabase(datasourceTmp.parseDbName(datasource.getJdbcUrl()));

        // 开始构造deployContainerReq
        submitWorkReq.setSparkSubmit(sparkSubmit);
        submitWorkReq.setPluginReq(pluginReq);
        submitWorkReq.setWorkType(WorkType.SPARK_CONTAINER_SQL);
        submitWorkReq.setWorkId(container.getId());
        submitWorkReq.setWorkInstanceId(container.getId());
        submitWorkReq.setAgentHomePath(engineNode.getAgentHomePath() + "/" + PathConstants.AGENT_PATH_NAME);
        submitWorkReq.setSparkHomePath(engineNode.getSparkHomePath());
        submitWorkReq.setClusterType(calculateEngineEntityOptional.get().getClusterType());

        // 构建容器完成，并打印容器配置信息
        submitLog.append(LocalDateTime.now()).append(WorkLog.SUCCESS_INFO).append("构建容器完成\n");

        // 提交返回对象
        DeployContainerRes deployContainerRes;

        // 开始提交容器
        BaseResponse<?> baseResponse;
        try {
            baseResponse = HttpUtils.doPost(
                genHttpUrl(engineNode.getHost(), engineNode.getAgentPort(), AgentUrl.DEPLOY_CONTAINER_URL),
                submitWorkReq, BaseResponse.class);
            if (!String.valueOf(HttpStatus.OK.value()).equals(baseResponse.getCode())) {
                throw new IsxAppException(
                    LocalDateTime.now() + WorkLog.ERROR_INFO + "提交容器失败 : " + baseResponse.getMsg() + "\n");
            }
            // 解析返回对象,获取appId
            if (baseResponse.getData() == null) {
                throw new IsxAppException(
                    LocalDateTime.now() + WorkLog.ERROR_INFO + "提交容器失败 : " + baseResponse.getMsg() + "\n");
            }
            deployContainerRes = JSON.parseObject(JSON.toJSONString(baseResponse.getData()), DeployContainerRes.class);
            submitLog.append(LocalDateTime.now()).append(WorkLog.SUCCESS_INFO).append("提交容器成功 : ")
                .append(deployContainerRes.getAppId()).append("\n");
            container = updateContainerSubmitLog(container, submitLog);

        } catch (ResourceAccessException e) {
            log.debug(e.getMessage(), e);
            throw new IsxAppException(LocalDateTime.now() + WorkLog.ERROR_INFO + "提交容器失败 : " + e.getMessage() + "\n");
        } catch (HttpServerErrorException e1) {
            if (HttpStatus.BAD_GATEWAY.value() == e1.getRawStatusCode()) {
                throw new IsxAppException(
                    LocalDateTime.now() + WorkLog.ERROR_INFO + "提交容器失败 : 无法访问节点服务器,请检查服务器防火墙或者计算集群\n");
            }
            throw new IsxAppException(LocalDateTime.now() + WorkLog.ERROR_INFO + "提交容器失败 : " + e1.getMessage() + "\n");
        }

        // 封装服务器请求体
        ScpFileEngineNodeDto scpFileEngineNodeDto =
            clusterNodeMapper.engineNodeEntityToScpFileEngineNodeDto(engineNode);
        scpFileEngineNodeDto.setPasswd(aesUtils.decrypt(scpFileEngineNodeDto.getPasswd()));

        // 开始循环判断容器是否运行成功
        String oldStatus = "";
        while (true) {

            // 获取容器状态并保存
            GetWorkStatusReq getWorkStatusReq = GetWorkStatusReq.builder().appId(deployContainerRes.getAppId())
                .clusterType(calculateEngineEntityOptional.get().getClusterType())
                .sparkHomePath(engineNode.getSparkHomePath()).build();
            baseResponse = HttpUtils.doPost(
                genHttpUrl(engineNode.getHost(), engineNode.getAgentPort(), AgentUrl.GET_WORK_STATUS_URL),
                getWorkStatusReq, BaseResponse.class);
            if (!String.valueOf(HttpStatus.OK.value()).equals(baseResponse.getCode())) {
                throw new IsxAppException(
                    LocalDateTime.now() + WorkLog.ERROR_INFO + "获取容器状态异常 : " + baseResponse.getMsg() + "\n");
            }

            // 解析返回状态，并保存
            RunWorkRes workStatusRes = JSON.parseObject(JSON.toJSONString(baseResponse.getData()), RunWorkRes.class);

            // 状态发生变化，则添加日志状态
            if (!oldStatus.equals(workStatusRes.getAppStatus())) {
                submitLog.append(LocalDateTime.now()).append(WorkLog.SUCCESS_INFO).append("运行状态:")
                    .append(workStatusRes.getAppStatus()).append("\n");
            }
            oldStatus = workStatusRes.getAppStatus();
            container = updateContainerSubmitLog(container, submitLog);

            // 如果状态是运行中，更新日志，继续执行
            List<String> runningStatus = Arrays.asList("RUNNING", "CONTAINERCREATING", "PENDING");
            List<String> submitStatus = Arrays.asList("UNDEFINED", "SUBMITTED");
            if (runningStatus.contains(workStatusRes.getAppStatus().toUpperCase())) {

                // 如果是k8s计算集群，推送开启端口号映射
                if (AgentType.K8S.equals(calculateEngineEntityOptional.get().getClusterType())) {
                    try {
                        scpText(scpFileEngineNodeDto,
                            "#!/bin/bash \n" + "\n" + "source /etc/profile " + "\n" + "kubectl port-forward "
                                + deployContainerRes.getAppId() + " " + deployContainerRes.getPort() + ":"
                                + deployContainerRes.getPort() + " -n zhiqingyun-space  &",
                            engineNode.getAgentHomePath() + "/zhiqingyun-agent/works/" + container.getId() + ".sh");
                        String executeBashWorkCommand = "nohup sh " + engineNode.getAgentHomePath()
                            + "/zhiqingyun-agent/works/" + container.getId() + ".sh >> " + engineNode.getAgentHomePath()
                            + "/zhiqingyun-agent/works/" + container.getId() + ".log 2>&1 & echo $!";
                        executeCommand(scpFileEngineNodeDto, executeBashWorkCommand, false);
                    } catch (JSchException | InterruptedException | IOException | SftpException e) {
                        log.info("映射端口号异常:{}", e.getMessage());
                    }
                }

                // 再次调用容器的check接口，确认容器是否成功启动
                ContainerCheckReq containerCheckReq = ContainerCheckReq.builder()
                    .port(String.valueOf(deployContainerRes.getPort())).appId(deployContainerRes.getAppId())
                    .agentType(calculateEngineEntityOptional.get().getClusterType()).build();
                baseResponse = HttpUtils.doPost(
                    genHttpUrl(engineNode.getHost(), engineNode.getAgentPort(), AgentUrl.CONTAINER_CHECK_URL),
                    containerCheckReq, BaseResponse.class);

                if (!String.valueOf(HttpStatus.OK.value()).equals(baseResponse.getCode())) {
                    if (!baseResponse.getMsg().contains(
                        "nested exception is java.net.ConnectException: Connection refused (Connection refused)")) {
                        throw new IsxAppException(
                            LocalDateTime.now() + WorkLog.ERROR_INFO + "获取容器状态异常 : " + baseResponse.getMsg() + "\n");
                    }
                }

                ContainerCheckRes containerCheckRes =
                    JSON.parseObject(JSON.toJSONString(baseResponse), ContainerCheckRes.class);
                if ("200".equals(containerCheckRes.getCode())) {

                    submitLog.append(LocalDateTime.now()).append(WorkLog.SUCCESS_INFO).append("运行状态:").append("容器启动成功")
                        .append("\n");
                    updateContainerSubmitLog(container, submitLog);
                    return DeployContainerRes.builder().port(deployContainerRes.getPort())
                        .appId(deployContainerRes.getAppId()).build();
                }

                try {
                    Thread.sleep(2000);
                } catch (InterruptedException e) {
                    throw new IsxAppException(
                        LocalDateTime.now() + WorkLog.ERROR_INFO + "睡眠线程异常 : " + e.getMessage() + "\n");
                }
            } else if (submitStatus.contains(workStatusRes.getAppStatus().toUpperCase())) {
                try {
                    Thread.sleep(2000);
                } catch (InterruptedException e) {
                    throw new IsxAppException(
                        LocalDateTime.now() + WorkLog.ERROR_INFO + "睡眠线程异常 : " + e.getMessage() + "\n");
                }
            } else {
                // 启动失败
                throw new IsxAppException(LocalDateTime.now() + WorkLog.ERROR_INFO + "启动失败\n");
            }
        }
    }

    public Map<String, String> genSparkSubmitConfig(Map<String, String> sparkConfig) {

        // 过滤掉，前缀不包含spark.xxx的配置，spark submit中必须都是spark.xxx
        Map<String, String> sparkSubmitConfig = new HashMap<>();
        sparkConfig.forEach((k, v) -> {
            if (k.startsWith("spark")) {
                sparkSubmitConfig.put(k, v);
            }
        });
        return sparkSubmitConfig;
    }

    public String genHttpUrl(String host, String port, String path) {

        String httpProtocol = isxAppProperties.isUseSsl() ? "https://" : "http://";
        String httpHost = isxAppProperties.isUsePort() ? host + ":" + port : host;

        return httpProtocol + httpHost + path;
    }
}
