package com.isxcode.star.vip.modules.auth.sso;

import com.isxcode.star.backend.api.base.exceptions.IsxAppException;
import com.isxcode.star.modules.auth.entity.AuthEntity;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RequiredArgsConstructor
public abstract class SsoAuth {

    public abstract String getSsoType();

    public abstract String getAccount(AuthEntity authEntity, String code);

    public abstract String getInvokeUrl(AuthEntity authEntity);

    public String getAccountStr(AuthEntity authEntity, String code) {

        try {
            return getAccount(authEntity, code);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new IsxAppException("获取account异常");
        }
    }
}
