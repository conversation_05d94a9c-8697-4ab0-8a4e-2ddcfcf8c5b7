package com.isxcode.star.vip.modules.work.quartz;

import static com.isxcode.star.common.config.CommonConfig.TENANT_ID;
import static com.isxcode.star.common.config.CommonConfig.USER_ID;

import com.alibaba.fastjson2.JSON;
import com.isxcode.star.modules.work.entity.WorkInstanceEntity;
import com.isxcode.star.modules.work.repository.WorkInstanceRepository;
import com.isxcode.star.modules.workflow.run.WorkflowRunEvent;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.quartz.*;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@RequiredArgsConstructor
public class QuartzWorkJob implements Job {

    private final Scheduler scheduler;

    private final ApplicationEventPublisher eventPublisher;

    private final WorkInstanceRepository workInstanceRepository;

    @Override
    public void execute(JobExecutionContext context) {

        // 解析定时器上下文
        QuartzWorkJobContext quartzWorkJobContext =
            JSON.parseObject(String.valueOf(context.getJobDetail().getJobDataMap().get("quartzWorkJobContext")),
                QuartzWorkJobContext.class);
        WorkflowRunEvent workRunEvent = quartzWorkJobContext.getWorkRunEvent();

        // 初始化异步线程中的上下文
        USER_ID.set(workRunEvent.getUserId());
        TENANT_ID.set(workRunEvent.getTenantId());

        // 修改作业实例，被触发过
        WorkInstanceEntity workInstance = workInstanceRepository
            .findByWorkIdAndWorkflowInstanceId(workRunEvent.getWorkId(), workRunEvent.getFlowInstanceId());
        workInstance.setQuartzHasRun(true);
        workInstance.setPlanStartDateTime(context.getScheduledFireTime());
        workInstanceRepository.saveAndFlush(workInstance);

        // 立马停止定时器
        try {
            scheduler.unscheduleJob(TriggerKey.triggerKey(workInstance.getId()));
        } catch (SchedulerException e) {
            log.error(e.getMessage(), e);
            log.info("工作流定时失效:{}", workInstance.getId());
            throw new RuntimeException(e);
        }

        // 推送给事件执行
        eventPublisher.publishEvent(quartzWorkJobContext.getWorkRunEvent());
    }
}
