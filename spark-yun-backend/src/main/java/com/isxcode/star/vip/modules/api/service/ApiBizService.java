package com.isxcode.star.vip.modules.api.service;

import com.alibaba.fastjson.*;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.isxcode.star.api.api.constants.ApiStatus;
import com.isxcode.star.api.api.constants.ApiType;
import com.isxcode.star.api.api.constants.TokenType;
import com.isxcode.star.api.api.dto.GetReqParamDto;
import com.isxcode.star.api.api.dto.HeaderTokenDto;
import com.isxcode.star.api.api.dto.JsonItemDto;
import com.isxcode.star.api.api.req.*;
import com.isxcode.star.api.api.res.*;
import com.isxcode.star.api.datasource.constants.ColumnType;
import com.isxcode.star.api.datasource.constants.DatasourceType;
import com.isxcode.star.api.datasource.dto.ConnectInfo;
import com.isxcode.star.api.datasource.dto.SecurityColumnDto;
import com.isxcode.star.backend.api.base.constants.SecurityConstants;
import com.isxcode.star.backend.api.base.exceptions.IsxAppException;
import com.isxcode.star.backend.api.base.properties.IsxAppProperties;
import com.isxcode.star.common.utils.jwt.JwtUtils;
import com.isxcode.star.modules.api.entity.ApiEntity;
import com.isxcode.star.modules.datasource.entity.DatasourceEntity;
import com.isxcode.star.modules.datasource.mapper.DatasourceMapper;
import com.isxcode.star.modules.datasource.service.DatasourceService;
import com.isxcode.star.modules.datasource.source.DataSourceFactory;
import com.isxcode.star.modules.datasource.source.Datasource;
import com.isxcode.star.modules.tenant.service.TenantService;
import com.isxcode.star.modules.api.mapper.ApiMapper;
import com.isxcode.star.modules.api.repository.ApiRepository;
import com.isxcode.star.modules.user.service.UserService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.logging.log4j.util.Strings;
import org.springframework.boot.autoconfigure.web.ServerProperties;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import javax.servlet.http.HttpServletRequest;
import javax.transaction.Transactional;
import java.sql.*;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;

import static com.isxcode.star.common.config.CommonConfig.TENANT_ID;
import static com.isxcode.star.common.config.CommonConfig.USER_ID;

@Service
@Slf4j
@RequiredArgsConstructor
@Transactional
public class ApiBizService {

    private final ApiMapper apiMapper;

    private final ApiRepository apiRepository;

    private final DatasourceService datasourceService;

    private final ApiService apiService;

    private final IsxAppProperties isxAppProperties;

    private final TenantService tenantService;

    private final ServerProperties serverProperties;

    private final DataSourceFactory dataSourceFactory;

    private final DatasourceMapper datasourceMapper;

    private final UserService userService;

    public AddApiRes addApi(AddApiReq addApiReq) {

        // 判断数据源是否存在
        datasourceService.getDatasource(addApiReq.getDatasourceId());

        // 判断path和apiType不能重复
        Optional<ApiEntity> apiEntityOptional =
            apiRepository.findByPathAndApiType(addApiReq.getPath(), addApiReq.getApiType());
        if (apiEntityOptional.isPresent()) {
            throw new IsxAppException("访问路径重复");
        }

        // 名称不能重复
        apiRepository.findByName(addApiReq.getName()).ifPresent(apiEntity -> {
            throw new IsxAppException("接口名称重复");
        });

        // 校验返回体的json配置
        boolean resValid = JSON.isValid(addApiReq.getResBody());
        if (!resValid) {
            throw new IsxAppException("返回体json配置异常");
        }

        // 校验请求体的配置
        if (!Strings.isEmpty(addApiReq.getReqBody())) {
            if (ApiType.POST.equals(addApiReq.getApiType())) {
                boolean reqValid = JSON.isValid(addApiReq.getReqBody());
                if (!reqValid) {
                    throw new IsxAppException("请求体json配置异常");
                }
            } else {
                try {
                    String[] split = addApiReq.getReqBody().split("&");
                    for (String s : split) {
                        if (!Strings.isEmpty(s)) {
                            String[] split1 = s.split("=\\$");
                            split1[1].split("\\.");
                        }
                    }
                } catch (Exception e) {
                    log.debug(e.getMessage(), e);
                    throw new IsxAppException("请求体格式异常,检查请求体配置");
                }
            }
        }

        // 校验sql是否合法
        if (!Strings.isEmpty(addApiReq.getApiSql())) {
            boolean sqlValid = datasourceService.checkSqlValid(addApiReq.getApiSql());
            if (!sqlValid) {
                throw new IsxAppException("sql设置异常");
            }
        }

        ApiEntity apiEntity = apiMapper.apiAddApiReqToApiEntity(addApiReq);
        if (addApiReq.getReqHeader() != null) {
            apiEntity.setReqHeader(JSON.toJSONString(addApiReq.getReqHeader()));
        }
        apiEntity.setStatus(ApiStatus.UNPUBLISHED);

        ApiEntity save = apiRepository.save(apiEntity);

        return AddApiRes.builder().id(save.getId()).build();
    }

    public UpdateApiRes updateApi(UpdateApiReq updateApiReq) {

        ApiEntity apiEntity = apiService.getApi(updateApiReq.getId());

        // 判断path和apiType不能重复
        Optional<ApiEntity> apiEntityOptional =
            apiRepository.findByPathAndApiType(updateApiReq.getPath(), updateApiReq.getApiType());
        if (apiEntityOptional.isPresent() && !apiEntityOptional.get().getId().equals(updateApiReq.getId())) {
            throw new IsxAppException("访问路径重复");
        }

        // 接口名称不能重复
        apiRepository.findByName(updateApiReq.getName()).ifPresent(api -> {
            if (!api.getId().equals(updateApiReq.getId())) {
                throw new IsxAppException("接口名称重复");
            }
        });

        // 校验返回体和请求体的json配置
        boolean resValid = JSON.isValid(updateApiReq.getResBody());
        if (!resValid) {
            throw new IsxAppException("返回体json配置异常");
        }
        if (ApiType.POST.equals(updateApiReq.getApiType())) {
            boolean reqValid = JSON.isValid(updateApiReq.getReqBody());
            if (!reqValid) {
                throw new IsxAppException("返回体json配置异常");
            }
        }

        // 校验sql是否合法
        if (!Strings.isEmpty(updateApiReq.getApiSql())) {
            boolean sqlValid = datasourceService.checkSqlValid(updateApiReq.getApiSql());
            if (!sqlValid) {
                throw new IsxAppException("sql设置异常");
            }
        }

        apiEntity = apiMapper.updateApiReqToApiEntity(updateApiReq, apiEntity);
        if (updateApiReq.getReqHeader() != null) {
            apiEntity.setReqHeader(JSON.toJSONString(updateApiReq.getReqHeader()));
        }

        ApiEntity save = apiRepository.save(apiEntity);

        return UpdateApiRes.builder().id(save.getId()).build();
    }

    public Page<PageApiRes> pageApi(PageApiReq pageApiReq) {

        Page<ApiEntity> apiEntities = apiRepository.pageApi(pageApiReq.getSearchKeyWord(),
            PageRequest.of(pageApiReq.getPage(), pageApiReq.getPageSize()));

        Page<PageApiRes> map = apiEntities.map(apiMapper::apiEntityToPageApiRes);

        map.getContent().forEach(e -> e.setCreateUsername(userService.getUserName(e.getCreateBy())));

        return map;
    }

    public void publishApi(PublishApiReq publishApiReq) {

        ApiEntity api = apiService.getApi(publishApiReq.getId());

        api.setStatus(ApiStatus.PUBLISHED);
        apiRepository.save(api);
    }

    public void offlineApi(OfflineApiReq offlineApiReq) {

        ApiEntity api = apiService.getApi(offlineApiReq.getId());

        api.setStatus(ApiStatus.UNPUBLISHED);
        apiRepository.save(api);
    }

    public void deleteApi(DeleteApiReq deleteApiReq) {

        ApiEntity api = apiService.getApi(deleteApiReq.getId());

        apiRepository.deleteById(api.getId());
    }

    public ResponseEntity<Object> postApi(HttpServletRequest httpServletRequest, String tenantId, String path,
        Object requestBody) {

        // 设置租户id
        TENANT_ID.set(tenantId);

        // 校验tenantId是否存在
        tenantService.getTenant(tenantId);

        // 判断地址是否存在
        ApiEntity api = apiRepository.findByPathAndApiType("/" + path, ApiType.POST)
            .orElseThrow(() -> new IsxAppException("404", "Api不存在"));

        // 如果是测试调用，则不需要发布
        if (!"self".equals(httpServletRequest.getHeader("spark-yun"))) {
            if (!ApiStatus.PUBLISHED.equals(api.getStatus())) {
                throw new IsxAppException("500", "自定义接口未发布");
            }
        }

        // 检验用户token
        checkApiToken(api, httpServletRequest);

        // 如果sql为空，直接返回resBody
        if (Strings.isEmpty(api.getApiSql())) {
            return new ResponseEntity<>(JSON.parseObject(api.getResBody()), HttpStatus.OK);
        }

        // 开启分页sql中多加两个字段
        String sql = api.getApiSql();
        DatasourceEntity datasourceEntity = datasourceService.getDatasource(api.getDatasourceId());
        if (api.getPageType()) {
            Datasource datasource = dataSourceFactory.getDatasource(datasourceEntity.getDbType());
            sql = datasource.getPageSql(sql);
        }

        // 分析sql中的字段
        List<SecurityColumnDto> securityColumns = datasourceService.transSecurityColumns(sql);

        // 解析成安全执行sql
        String securitySql = datasourceService.transSecuritySql(sql);

        // 解析模版中请求参数的jsonpath
        Map<String, JsonItemDto> reqJsonPathMap = new HashMap<>();
        Map<String, Object> allItemPaths = JSONPath.paths(JSON.parseObject(api.getReqBody(), Map.class));
        allItemPaths.forEach((k, v) -> {
            // 开头存在$符号，则为请求参数
            if (String.valueOf(v).startsWith("$")) {
                String[] split = String.valueOf(v).split("\\.");
                reqJsonPathMap.put(split[0].substring(2),
                    JsonItemDto.builder().jsonPath(k).type(split[1].replace("}", "")).build());
            }
        });

        // 遍历sql的变量，进行二次补齐
        final Integer[] page = new Integer[1];
        final Integer[] pageSize = new Integer[1];
        securityColumns.forEach(e -> {
            JsonItemDto jsonItemDto = reqJsonPathMap.get(e.getName().substring(3));
            if (jsonItemDto == null) {
                throw new IsxAppException("400", "缺少必要参数：" + e.getName().substring(3).replace(".", ""));
            }
            e.setType(transApiType(jsonItemDto));
            e.setValue(transApiReqValue(jsonItemDto, requestBody));

            // 解析出分页字段
            if (e.getName().endsWith(".page")) {
                page[0] = Integer.parseInt(String.valueOf(e.getValue()));
                if (page[0] < 1) {
                    throw new IsxAppException("page不能小于1");
                }
            } else if (e.getName().endsWith(".pageSize")) {
                pageSize[0] = Integer.parseInt(String.valueOf(e.getValue()));
                if (pageSize[0] < 1) {
                    throw new IsxAppException("pageSize不能小于1");
                }
            }
        });

        // 翻译解析分页的参数
        if (api.getPageType()) {
            securityColumns.forEach(e -> {
                if (DatasourceType.SQL_SERVER.equals(datasourceEntity.getDbType())
                    || DatasourceType.ORACLE.equals(datasourceEntity.getDbType())) {
                    if (e.getName().endsWith(".page")) {
                        e.setValue((page[0] - 1) * pageSize[0] + 1);
                    } else if (e.getName().endsWith(".pageSize")) {
                        e.setValue(page[0] * pageSize[0]);
                    }
                } else {
                    if (e.getName().endsWith(".page")) {
                        e.setValue((page[0] - 1) * pageSize[0]);
                    }
                }
            });
        }

        // 开始执行sql =====================================================
        return executeApi(api, securitySql, securityColumns, requestBody, reqJsonPathMap);
    }

    public ResponseEntity<Object> executeApi(ApiEntity api, String securitySql, List<SecurityColumnDto> securityColumns,
        Object requestBody, Map<String, JsonItemDto> reqJsonPathMap) {

        DatasourceEntity datasourceEntity = datasourceService.getDatasource(api.getDatasourceId());
        ConnectInfo connectInfo = datasourceMapper.datasourceEntityToConnectInfo(datasourceEntity);
        Datasource datasource = dataSourceFactory.getDatasource(connectInfo.getDbType());

        if (!datasourceService.isQueryStatement(api.getApiSql())) {
            // 非查询的sql，运行成功后，直接返回resBody
            datasource.securityExecuteSql(connectInfo, securitySql, securityColumns);
            return new ResponseEntity<>(JSON.parseObject(api.getResBody()), HttpStatus.OK);
        } else {
            // 查询sql

            if (JSON.isValidArray(api.getResBody())) {
                // 返回类型为数组: []
                Map<String, JsonItemDto> resJsonPathMap = new HashMap<>();
                JSONArray resultJsonArray = JSON.parseArray(api.getResBody());
                Object itemTemp = JSONPath.extract(api.getResBody(), "$[0]");
                Map<String, Object> resPaths = JSONPath.paths(itemTemp);
                resPaths.forEach((k, v) -> {
                    if (String.valueOf(v).startsWith("$")) {
                        String[] split = String.valueOf(v).split("\\.");
                        resJsonPathMap.put(split[0].substring(1).replace("{", ""), JsonItemDto.builder()
                            .express(String.valueOf(v)).jsonPath(k).type(split[1].replace("}", "")).build());
                    }
                });
                JSONPath.remove(resultJsonArray, "$[0]");
                try (Connection connection = datasource.getConnection(connectInfo);
                    PreparedStatement statement = connection.prepareStatement(securitySql);
                    ResultSet resultSet = datasource.securityQuerySql(statement, securityColumns)) {
                    while (resultSet.next()) {
                        String metaItemStr = String.valueOf(itemTemp);
                        int columnCount = resultSet.getMetaData().getColumnCount();
                        for (int i = 1; i <= columnCount; i++) {
                            String columnLabel = resultSet.getMetaData().getColumnLabel(i);
                            String columnName = resultSet.getMetaData().getColumnName(i);
                            String column = (columnLabel.equals(columnName) ? columnName : columnLabel);
                            JsonItemDto jsonItemDto = resJsonPathMap.get(column);
                            if (jsonItemDto == null) {
                                continue;
                            }
                            metaItemStr = metaItemStr.replace("\"" + jsonItemDto.getExpress() + "\"",
                                transApiResValue(jsonItemDto, resultSet, i));
                        }
                        JSONPath.arrayAdd(resultJsonArray, "$", JSON.parseObject(metaItemStr));
                    }
                } catch (Exception e) {
                    log.debug(e.getMessage(), e);
                    log.debug("executeApi:", e);
                    throw new IsxAppException("500", e.getMessage());
                }
                return new ResponseEntity<>(resultJsonArray, HttpStatus.OK);
            } else {
                // 判断对象中包含数组: {}
                Map<String, JsonItemDto> resJsonPathMap = new HashMap<>();
                AtomicReference<String> listDataPath = new AtomicReference<>();
                Map<String, Object> allResponsePaths = JSONPath.paths(JSON.parseObject(api.getResBody(), Map.class));
                allResponsePaths.forEach((k, v) -> {
                    if (v instanceof JSONArray && k.contains(".$")) {
                        listDataPath.set(k);
                    }
                    if (String.valueOf(v).startsWith("$")) {
                        String[] split = String.valueOf(v).split("\\.");
                        resJsonPathMap.put(split[0].substring(2), JsonItemDto.builder().express(String.valueOf(v))
                            .jsonPath(k).type(split[1].replace("}", "")).build());
                    }
                });

                if (Strings.isEmpty(listDataPath.get())) {
                    // 返回是单个对象
                    String metaItemStr = String.valueOf(api.getResBody());
                    try (Connection connection = datasource.getConnection(connectInfo);
                        PreparedStatement statement = connection.prepareStatement(securitySql);
                        ResultSet resultSet = datasource.securityQuerySql(statement, securityColumns)) {
                        int columnCount = resultSet.getMetaData().getColumnCount();
                        if (resultSet.next()) {
                            for (int i = 1; i <= columnCount; i++) {
                                String columnLabel = resultSet.getMetaData().getColumnLabel(i);
                                String columnName = resultSet.getMetaData().getColumnName(i);
                                String column = (columnLabel.equals(columnName) ? columnName : columnLabel);
                                JsonItemDto jsonItemDto = resJsonPathMap.get(column);
                                if (jsonItemDto == null) {
                                    continue;
                                }
                                metaItemStr = metaItemStr.replace("\"" + jsonItemDto.getExpress() + "\"",
                                    transApiResValue(jsonItemDto, resultSet, i));
                            }
                        } else {
                            metaItemStr = "{}";
                        }
                    } catch (Exception e) {
                        log.debug(e.getMessage(), e);
                        log.debug("executeApi:", e);
                        throw new IsxAppException("500", e.getMessage());
                    }
                    return new ResponseEntity<>(JSON.parseObject(metaItemStr), HttpStatus.OK);
                } else {
                    JSONObject resJsonObject = JSON.parseObject(api.getResBody());
                    JSONPath.remove(resJsonObject, listDataPath.get());
                    Object itemTemp = JSONPath.extract(api.getResBody(), listDataPath.get() + "[0]");
                    // 返回为多个对象的数组
                    try (Connection connection = datasource.getConnection(connectInfo);
                        PreparedStatement statement = connection.prepareStatement(securitySql);
                        ResultSet resultSet = datasource.securityQuerySql(statement, securityColumns)) {
                        int count = 0;
                        while (resultSet.next()) {
                            int columnCount = resultSet.getMetaData().getColumnCount();
                            String metaItemStr = String.valueOf(itemTemp);
                            for (int i = 1; i <= columnCount; i++) {
                                String columnLabel = resultSet.getMetaData().getColumnLabel(i);
                                if ("SY_ROW_NUM".equals(columnLabel)) {
                                    continue;
                                }
                                String columnName = resultSet.getMetaData().getColumnName(i);
                                String column = (columnLabel.equals(columnName) ? columnName : columnLabel);
                                JsonItemDto jsonItemDto =
                                    resJsonPathMap.get(column) == null ? resJsonPathMap.get(column.toLowerCase())
                                        : resJsonPathMap.get(column);
                                metaItemStr = metaItemStr.replace("\"" + jsonItemDto.getExpress() + "\"",
                                    transApiResValue(jsonItemDto, resultSet, i));
                            }
                            JSONPath.arrayAdd(resJsonObject, listDataPath.get().replace("$", ""),
                                JSON.parseObject(metaItemStr));
                            count++;
                        }
                        if (count == 0) {
                            JSONPath.arrayAdd(resJsonObject, listDataPath.get().replace("$", ""));
                        }
                    } catch (Exception e) {
                        log.debug(e.getMessage(), e);
                        throw new IsxAppException("500", "查询异常" + e.getMessage());
                    }

                    // 如果开启分页，且返回包含${count.long},则需要查询条数
                    if (api.getResBody().contains("${count.long}") && api.getPageType()) {
                        List<SecurityColumnDto> countSecurityColumns =
                            datasourceService.transSecurityColumns(api.getApiSql());

                        String countSecuritySql = "select count(1) as sum from ("
                            + datasourceService.transSecuritySql(api.getApiSql()) + ") as tmp";
                        if (DatasourceType.ORACLE.equals(datasourceEntity.getDbType())) {
                            countSecuritySql = "select count(1) as sum from ("
                                + datasourceService.transSecuritySql(api.getApiSql()) + ") tmp";
                        }

                        countSecurityColumns.forEach(e -> {
                            JsonItemDto jsonItemDto = reqJsonPathMap.get(e.getName().substring(3));
                            e.setType(transApiType(jsonItemDto));
                            if (ApiType.POST.equals(api.getApiType())) {
                                e.setValue(transApiReqValue(jsonItemDto, requestBody));
                            } else {
                                Map<String, String> requestParams = JSON.parseObject(JSON.toJSONString(requestBody),
                                    new TypeReference<Map<String, String>>() {});
                                e.setValue(transApiReqValueForGet(jsonItemDto, requestParams));
                            }
                        });
                        long count =
                            datasource.securityGetTableCount(connectInfo, countSecuritySql, countSecurityColumns);
                        String resultStr = resJsonObject.toString().replace("\"${count.long}\"", String.valueOf(count));
                        return new ResponseEntity<>(JSON.parseObject(resultStr), HttpStatus.OK);
                    } else {
                        return new ResponseEntity<>(resJsonObject, HttpStatus.OK);
                    }
                }
            }
        }
    }

    public ResponseEntity<Object> getApi(HttpServletRequest httpServletRequest, String tenantId, String path,
        Map<String, String> requestBody) {

        // 设置租户id
        TENANT_ID.set(tenantId);

        // 校验tenantId是否存在
        tenantService.getTenant(tenantId);

        // 判断地址是否存在
        ApiEntity api = apiRepository.findByPathAndApiType("/" + path, ApiType.GET)
            .orElseThrow(() -> new IsxAppException("404", "Api不存在"));

        // 如果是测试调用，则不需要发布
        if (!"self".equals(httpServletRequest.getHeader("spark-yun"))) {
            if (!ApiStatus.PUBLISHED.equals(api.getStatus())) {
                throw new IsxAppException("500", "自定义接口未发布");
            }
        }

        // 检查用户token
        checkApiToken(api, httpServletRequest);

        // 如果sql为空，直接返回
        if (Strings.isEmpty(api.getApiSql())) {
            return new ResponseEntity<>(JSON.parseObject(api.getResBody()), HttpStatus.OK);
        }

        // 开启分页sql中多加两个字段
        String sql = api.getApiSql();
        DatasourceEntity datasourceEntity = datasourceService.getDatasource(api.getDatasourceId());
        if (api.getPageType()) {
            Datasource datasource = dataSourceFactory.getDatasource(datasourceEntity.getDbType());
            sql = datasource.getPageSql(sql);
        }

        // 分析sql中的字段
        List<SecurityColumnDto> securityColumns = datasourceService.transSecurityColumns(sql);

        // 解析成安全执行sql
        String securitySql = datasourceService.transSecuritySql(sql);

        // 解析请求体中 page=$page.int&pageSize=$pageSize.int&username=$username.int
        Map<String, JsonItemDto> reqJsonPathMap = parseVariables(api.getReqBody());

        // 遍历sql的变量，进行二次补齐
        final Integer[] page = new Integer[1];
        final Integer[] pageSize = new Integer[1];
        securityColumns.forEach(e -> {
            JsonItemDto jsonItemDto = reqJsonPathMap.get(e.getName().substring(3));
            if (jsonItemDto == null) {
                throw new IsxAppException("400", "缺少必要参数：" + e.getName().substring(3).replace(".", ""));
            }
            e.setType(transApiType(jsonItemDto));
            try {
                Object value = transApiReqValueForGet(jsonItemDto, requestBody);
                if (value == null) {
                    throw new IsxAppException("400", "缺少必要参数：" + jsonItemDto.getExpress());
                }
                e.setValue(value);
            } catch (Exception exception) {
                log.debug(exception.getMessage(), exception);
                throw new IsxAppException("400", "缺少必要参数：" + jsonItemDto.getExpress());
            }

            // 解析出分页字段
            if (Strings.isEmpty(String.valueOf(e.getValue()))) {
                throw new IsxAppException("请求参数不能为空");
            }
            if (e.getName().endsWith(".page")) {
                page[0] = Integer.parseInt(String.valueOf(e.getValue()));
                if (page[0] < 1) {
                    throw new IsxAppException("page不能小于1且不为空");
                }
            } else if (e.getName().endsWith(".pageSize")) {
                pageSize[0] = Integer.parseInt(String.valueOf(e.getValue()));
                if (pageSize[0] < 1) {
                    throw new IsxAppException("pageSize不能小于1且不为空");
                }
            }
        });

        // 翻译解析分页的参数
        if (api.getPageType()) {
            securityColumns.forEach(e -> {
                if (DatasourceType.SQL_SERVER.equals(datasourceEntity.getDbType())
                    || DatasourceType.ORACLE.equals(datasourceEntity.getDbType())) {
                    if (e.getName().endsWith(".page")) {
                        e.setValue((page[0] - 1) * pageSize[0] + 1);
                    } else if (e.getName().endsWith(".pageSize")) {
                        e.setValue(page[0] * pageSize[0]);
                    }
                } else {
                    if (e.getName().endsWith(".page")) {
                        e.setValue((page[0] - 1) * pageSize[0]);
                    }
                }
            });
        }

        return executeApi(api, securitySql, securityColumns, requestBody, reqJsonPathMap);
    }

    private static Map<String, JsonItemDto> parseVariables(String input) {

        Map<String, JsonItemDto> resultMap = new HashMap<>();

        if (input != null) {
            String[] split = input.split("&");
            for (String s : split) {
                if (!Strings.isEmpty(s)) {
                    String[] split1 = s.split("=\\$");
                    String[] split2 = split1[1].split("\\.");
                    resultMap.put(split2[0].replace("{", ""),
                        JsonItemDto.builder().express(split1[0]).type(split2[1].replace("}", "")).build());
                }
            }
        }
        return resultMap;
    }

    public GetApiRes getApi(GetApiReq getApiReq) {

        ApiEntity api = apiService.getApi(getApiReq.getId());
        GetApiRes apiRes = apiMapper.apiEntityToGetApiRes(api);
        apiRes.setReqHeader(JSONArray.parseArray(api.getReqHeader(), HeaderTokenDto.class));

        // 返回模版
        if (ApiType.POST.equals(api.getApiType())) {

            // 解析模版中请求参数的jsonpath
            AtomicReference<String> reqJson = new AtomicReference<>(api.getReqBody());
            Map<String, Object> allItemPaths = JSONPath.paths(JSON.parseObject(api.getReqBody(), Map.class));
            allItemPaths.forEach((k, v) -> {
                if (String.valueOf(v).startsWith("$")) {
                    String[] split = String.valueOf(v).split("\\.");
                    reqJson.set(reqJson.get().replace("\"" + v + "\"",
                        transReqTempValue(split[1].replace("}", ""), api.getApiType())));
                }
            });
            apiRes.setReqJsonTemp(JSON.toJSONString(reqJson));
        } else {
            List<GetReqParamDto> reqGetTemp = new ArrayList<>();
            if (api.getReqBody() != null) {
                String[] split = api.getReqBody().split("&");
                for (String s : split) {
                    if (!Strings.isEmpty(s)) {
                        String[] split1 = s.split("=\\$");
                        String[] split2 = split1[1].split("\\.");
                        reqGetTemp.add(GetReqParamDto.builder().label(split1[0])
                            .value(transReqTempValue(split2[1].replace("}", ""), api.getApiType())).build());
                    }
                }
            }
            apiRes.setReqGetTemp(reqGetTemp);
        }

        return apiRes;
    }

    public TestApiRes testApi(TestApiReq testApiReq) {

        ApiEntity api = apiService.getApi(testApiReq.getId());

        String httpProtocol = isxAppProperties.isUseSsl() ? "https://" : "http://";
        int port = isxAppProperties.isDockerMode() ? 8080 : serverProperties.getPort();
        StringBuilder httpUrlBuilder =
            new StringBuilder(httpProtocol + "127.0.0.1:" + port + "/" + TENANT_ID.get() + "/api" + api.getPath());

        // 封装请求头
        HttpHeaders headers = new HttpHeaders();
        headers.add("spark-yun", "self");
        if (testApiReq.getHeaderParams() != null) {
            testApiReq.getHeaderParams().forEach(headers::add);
        }

        // post接口
        if (ApiType.POST.equals(api.getApiType())) {
            headers.add(HttpHeaders.CONTENT_TYPE, "application/json;charset=UTF-8");
        }

        // get接口拼接参数
        if (ApiType.GET.equals(api.getApiType())) {
            if (testApiReq.getRequestBody() != null) {
                Map<String, String> requestParams = JSON.parseObject(JSON.toJSONString(testApiReq.getRequestBody()),
                    new TypeReference<Map<String, String>>() {});
                httpUrlBuilder.append("?");
                requestParams.forEach((k, v) -> httpUrlBuilder.append(k).append("=").append(v).append("&"));
            }
        }

        // post接口封装请求体
        HttpEntity<String> requestEntity;
        if (ApiType.GET.equals(api.getApiType())) {
            requestEntity = new HttpEntity<>(null, headers);
        } else {
            try {
                requestEntity =
                    new HttpEntity<>(new ObjectMapper().writeValueAsString(testApiReq.getRequestBody()), headers);
            } catch (JsonProcessingException e) {
                throw new IsxAppException("requestBody请求异常");
            }
        }

        // 调用接口
        try {
            ResponseEntity<Object> resultEntity = new RestTemplate().exchange(httpUrlBuilder.toString(),
                ApiType.GET.equals(api.getApiType()) ? HttpMethod.GET : HttpMethod.POST, requestEntity, Object.class);
            return TestApiRes.builder().body(resultEntity.getBody()).httpStatus(resultEntity.getStatusCodeValue())
                .build();
        } catch (Exception e) {
            return TestApiRes.builder().msg(e.getMessage()).httpStatus(HttpStatus.INTERNAL_SERVER_ERROR.value())
                .build();
        }
    }

    public void checkApiToken(ApiEntity api, HttpServletRequest httpServletRequest) {

        // 判断token的类型
        switch (api.getTokenType()) {
            case TokenType.SYSTEM:
                // 系统认证
                String token = httpServletRequest.getHeader((SecurityConstants.HEADER_AUTHORIZATION));
                if (Strings.isEmpty(token)) {
                    throw new IsxAppException("403", "令牌为空，权限不足");
                }
                try {
                    // 获取用户userId
                    String userId = JwtUtils.decrypt(isxAppProperties.getJwtKey(), token, isxAppProperties.getAesSlat(),
                        String.class);
                    USER_ID.set(userId);
                } catch (Exception e) {
                    throw new IsxAppException("403", "权限不足");
                }
                break;
            case TokenType.CUSTOM:
                // 用户自定义
                List<HeaderTokenDto> headerTokenList = JSON.parseArray(api.getReqHeader(), HeaderTokenDto.class);
                headerTokenList.forEach(e -> {
                    String header = httpServletRequest.getHeader(e.getLabel());
                    if (Strings.isEmpty(header)) {
                        throw new IsxAppException("403", e.getLabel() + "为空，权限不足");
                    }
                    if (!e.getValue().equals(header)) {
                        throw new IsxAppException("403", "权限不足");
                    }
                });
                break;
        }
    }

    public String transReqTempValue(String type, String apiType) {

        if (ApiType.POST.equals(apiType)) {
            switch (type) {
                case "date":
                    return "\"2024-12-12\"";
                case "datetime":
                    return "\"2024-12-12 12:12:12\"";
                case "timestamp":
                    return "63874281542";
                case "string":
                    return "\"\"";
                case "boolean":
                    return "true";
                case "double":
                    return "12.00";
                case "int":
                    return "12";
                default:
                    throw new IsxAppException("该类型不支持");
            }
        } else {
            switch (type) {
                case "date":
                    return "2024-12-12";
                case "datetime":
                    return "2024-12-12 12:12:12";
                case "timestamp":
                    return "63874281542";
                case "string":
                    return "";
                case "boolean":
                    return "true";
                case "double":
                    return "12.00";
                case "int":
                    return "12";
                default:
                    throw new IsxAppException("该类型不支持");
            }
        }
    }

    public String transApiType(JsonItemDto jsonItemDto) {

        switch (jsonItemDto.getType()) {
            case "date":
                return ColumnType.DATE;
            case "datetime":
                return ColumnType.DATE_TIME;
            case "timestamp":
                return ColumnType.TIMESTAMP;
            case "string":
                return ColumnType.STRING;
            case "boolean":
                return ColumnType.BOOLEAN;
            case "double":
                return ColumnType.DOUBLE;
            case "int":
                return ColumnType.INT;
            default:
                throw new IsxAppException("该类型不支持");
        }
    }

    public Object transApiReqValue(JsonItemDto jsonItemDto, Object requestBody) {

        Object value = JSONPath.extract(JSON.toJSONString(requestBody), jsonItemDto.getJsonPath());

        switch (jsonItemDto.getType()) {
            case "date":
                String dateStr = String.valueOf(value);
                LocalDate date = LocalDate.parse(dateStr);
                LocalDateTime dateTimeForDate = date.atStartOfDay();
                return dateTimeForDate.toInstant(ZoneOffset.ofHours(8)).toEpochMilli();
            case "datetime":
                String datetimeStr = String.valueOf(value);
                LocalDateTime datetime =
                    LocalDateTime.parse(datetimeStr, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
                return datetime.toInstant(ZoneOffset.ofHours(8)).toEpochMilli();
            default:
                return value;
        }
    }

    public Object transApiReqValueForGet(JsonItemDto jsonItemDto, Map<String, String> requestBody) {

        switch (jsonItemDto.getType()) {
            case "date":
                String dateStr = String.valueOf(requestBody.get(jsonItemDto.getExpress()));
                LocalDate date = LocalDate.parse(dateStr);
                LocalDateTime dateTimeForDate = date.atStartOfDay();
                return dateTimeForDate.toInstant(ZoneOffset.ofHours(8)).toEpochMilli();
            case "datetime":
                String datetimeStr = String.valueOf(requestBody.get(jsonItemDto.getExpress()));
                LocalDateTime datetime =
                    LocalDateTime.parse(datetimeStr, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
                return datetime.toInstant(ZoneOffset.ofHours(8)).toEpochMilli();
            default:
                return requestBody.get(jsonItemDto.getExpress());
        }
    }

    public String transApiResValue(JsonItemDto jsonItemDto, ResultSet resultSet, int colIndex) throws SQLException {

        try {
            Object object = resultSet.getObject(colIndex);
            if (object == null) {
                return "null";
            }
        } catch (SQLException e) {
            if ("S1000".equals(e.getSQLState())) {
                return "null";
            }
        }

        switch (jsonItemDto.getType()) {

            case "date":
                return "\"" + resultSet.getDate(colIndex) + "\"";
            case "datetime":
                return "\"" + resultSet.getDate(colIndex) + " " + resultSet.getTime(colIndex) + "\"";
            case "timestamp":
                return String.valueOf(resultSet.getTimestamp(colIndex).getTime());
            case "int":
                return String.valueOf(resultSet.getInt(colIndex));
            case "boolean":
                return String.valueOf(resultSet.getBoolean(colIndex));
            case "double":
                return String.valueOf(resultSet.getDouble(colIndex));
            default:
                return "\"" + resultSet.getString(colIndex) + "\"";
        }
    }

}
