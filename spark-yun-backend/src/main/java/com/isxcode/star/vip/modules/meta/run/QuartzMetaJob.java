package com.isxcode.star.vip.modules.meta.run;

import com.alibaba.fastjson2.JSON;
import com.isxcode.star.api.meta.constant.MetaInstanceStatus;
import com.isxcode.star.api.meta.constant.MetaTriggerType;
import com.isxcode.star.modules.meta.entity.MetaInstanceEntity;
import com.isxcode.star.modules.meta.entity.MetaWorkEntity;
import com.isxcode.star.modules.meta.repository.MetaInstanceRepository;
import com.isxcode.star.vip.modules.meta.service.MetaService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.quartz.Job;
import org.quartz.JobExecutionContext;
import org.springframework.stereotype.Component;

import javax.transaction.Transactional;

import java.time.LocalDateTime;

import static com.isxcode.star.common.config.CommonConfig.TENANT_ID;
import static com.isxcode.star.common.config.CommonConfig.USER_ID;

@Slf4j
@Component
@RequiredArgsConstructor
public class QuartzMetaJob implements Job {

    private final CollectMetaService collectMetaService;

    private final MetaService metaService;

    private final MetaInstanceRepository metaInstanceRepository;

    @Override
    @Transactional
    public void execute(JobExecutionContext context) {

        // 解析定时器上下文
        QuartzMetaWork quartzMetaWork = JSON.parseObject(
            String.valueOf(context.getJobDetail().getJobDataMap().get("quartzMetaWork")), QuartzMetaWork.class);

        USER_ID.set(quartzMetaWork.getUserId());
        TENANT_ID.set(quartzMetaWork.getTenantId());

        MetaWorkEntity metaWork = metaService.getMetaWork(quartzMetaWork.getMetaWorkId());

        MetaInstanceEntity metaInstance =
            MetaInstanceEntity.builder().metaWorkId(metaWork.getId()).status(MetaInstanceStatus.COLLECTING)
                .startDateTime(LocalDateTime.now()).triggerType(MetaTriggerType.AUTO_TRIGGER).build();
        metaInstance = metaInstanceRepository.saveAndFlush(metaInstance);

        collectMetaService.collect(metaWork, metaInstance.getId());
    }
}
