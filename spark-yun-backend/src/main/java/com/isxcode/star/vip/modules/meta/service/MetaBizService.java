package com.isxcode.star.vip.modules.meta.service;

import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.unit.DataSizeUtil;
import cn.hutool.poi.excel.ExcelUtil;
import cn.hutool.poi.excel.ExcelWriter;
import com.alibaba.fastjson.JSON;
import com.isxcode.star.api.datasource.constants.DatasourceStatus;
import com.isxcode.star.api.datasource.constants.DatasourceType;
import com.isxcode.star.api.meta.ao.MetaTableAo;
import com.isxcode.star.api.datasource.dto.ConnectInfo;
import com.isxcode.star.api.datasource.dto.QueryColumnDto;
import com.isxcode.star.api.meta.constant.MetaDatabaseStatus;
import com.isxcode.star.api.meta.constant.MetaInstanceStatus;
import com.isxcode.star.api.meta.constant.MetaTriggerType;
import com.isxcode.star.api.meta.constant.MetaWorkStatus;
import com.isxcode.star.api.meta.ao.MetaColumnAo;
import com.isxcode.star.api.meta.req.*;
import com.isxcode.star.api.meta.res.*;
import com.isxcode.star.api.work.dto.CronConfig;
import com.isxcode.star.api.work.res.GetDataSourceDataRes;
import com.isxcode.star.backend.api.base.exceptions.IsxAppException;
import com.isxcode.star.modules.datasource.entity.DatasourceEntity;
import com.isxcode.star.modules.datasource.mapper.DatasourceMapper;
import com.isxcode.star.modules.datasource.repository.DatasourceRepository;
import com.isxcode.star.modules.datasource.service.DatasourceService;
import com.isxcode.star.modules.datasource.source.DataSourceFactory;
import com.isxcode.star.modules.datasource.source.Datasource;
import com.isxcode.star.modules.meta.entity.*;
import com.isxcode.star.modules.meta.repository.*;
import com.isxcode.star.modules.meta.mapper.MetaMapper;
import com.isxcode.star.vip.modules.meta.run.CollectMetaService;
import com.isxcode.star.vip.modules.meta.run.QuartzMetaJob;
import com.isxcode.star.vip.modules.meta.run.QuartzMetaWork;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.logging.log4j.util.Strings;
import org.quartz.*;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import javax.transaction.Transactional;
import java.io.IOException;
import java.net.URLEncoder;
import java.text.ParseException;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

import static com.isxcode.star.common.config.CommonConfig.*;
import static com.isxcode.star.vip.modules.meta.run.CollectMetaService.META_WORK_THREAD;

@Service
@RequiredArgsConstructor
@Transactional
@Slf4j
public class MetaBizService {

    private final CollectMetaService collectMeta;

    private final MetaDatabaseRepository metaDatabaseRepository;

    private final DatasourceRepository datasourceRepository;

    private final MetaMapper metaMapper;

    private final MetaTableRepository metaTableRepository;

    private final MetaColumnRepository metaColumnRepository;

    private final MetaWorkRepository metaWorkRepository;

    private final DatasourceService datasourceService;

    private final MetaService metaService;

    private final MetaInstanceRepository metaInstanceRepository;

    private final Scheduler scheduler;

    private final MetaTableInfoRepository metaTableInfoRepository;

    private final DataSourceFactory dataSourceFactory;

    private final DatasourceMapper datasourceMapper;

    private final MetaColumnInfoRepository metaColumnInfoRepository;

    public Page<PageMetaDatabaseRes> pageMetaDatabase(PageMetaDatabaseReq pageMetaDatabaseReq) {

        if (pageMetaDatabaseReq.getSearchKeyWord() == null) {
            pageMetaDatabaseReq.setSearchKeyWord("");
        }

        Page<MetaDatabaseEntity> metaDatabasePage =
            metaDatabaseRepository.searchAll(pageMetaDatabaseReq.getSearchKeyWord(),
                PageRequest.of(pageMetaDatabaseReq.getPage(), pageMetaDatabaseReq.getPageSize()));

        // 如果null值默认给ACTIVE
        metaDatabasePage.getContent()
            .forEach(e -> e.setStatus(Strings.isEmpty(e.getStatus()) ? MetaDatabaseStatus.ACTIVE : e.getStatus()));

        return metaDatabasePage.map(metaMapper::metaDatabaseEntityToPageMetaDatabaseRes);
    }

    public void refreshMetaDatabase() {

        // 查询现存的数据源
        List<DatasourceEntity> allDatasource = datasourceRepository.findAll().stream()
            .filter(e -> DatasourceStatus.ACTIVE.equals(e.getStatus())).collect(Collectors.toList());
        Map<String, DatasourceEntity> datasourceMap = allDatasource.stream()
            .collect(Collectors.toMap(DatasourceEntity::getId, entity -> entity, (existing, replacement) -> existing));

        // 查询现存的元数据
        List<MetaDatabaseEntity> allMetaDatasource = metaDatabaseRepository.findAll();
        Map<String, MetaDatabaseEntity> metaDatasourceMap = allMetaDatasource.stream().collect(Collectors
            .toMap(MetaDatabaseEntity::getDatasourceId, entity -> entity, (existing, replacement) -> existing));

        // 遍历获取需要新增的数据源元数据
        List<MetaDatabaseEntity> saveAllMetaDatasource = new ArrayList<>();
        allDatasource.stream().filter(e -> !DatasourceType.KAFKA.equals(e.getDbType())).forEach(datasource -> {
            MetaDatabaseEntity metaDatabaseEntity = metaDatasourceMap.get(datasource.getId());
            if (metaDatabaseEntity == null) {
                metaDatabaseEntity = new MetaDatabaseEntity();
                metaDatabaseEntity.setDatasourceId(datasource.getId());
            }
            Datasource datasourceTmp = dataSourceFactory.getDatasource(datasource.getDbType());
            metaDatabaseEntity.setDbName(datasourceTmp.parseDbName(datasource.getJdbcUrl()));
            metaDatabaseEntity.setName(datasource.getName());
            if (Strings.isEmpty(metaDatabaseEntity.getDbComment())) {
                metaDatabaseEntity.setDbComment(datasource.getRemark());
            }
            metaDatabaseEntity.setDbType(datasource.getDbType());
            metaDatabaseEntity.setStatus(MetaDatabaseStatus.ACTIVE);
            saveAllMetaDatasource.add(metaDatabaseEntity);
        });

        metaDatabaseRepository.saveAll(saveAllMetaDatasource);

        // 遍历需要删除的数据源元数据
        List<MetaDatabaseEntity> deleteAllMetaDatasource = new ArrayList<>();
        allMetaDatasource.forEach(metaDatabase -> {
            if (datasourceMap.get(metaDatabase.getDatasourceId()) == null) {
                metaDatabase.setStatus(MetaDatabaseStatus.DELETED);
                deleteAllMetaDatasource.add(metaDatabase);
            } ;
        });
        metaDatabaseRepository.saveAll(deleteAllMetaDatasource);
    }

    public Page<PageMetaTableRes> pageMetaTable(PageMetaTableReq pageMetaTableReq) {

        JPA_TENANT_MODE.set(false);
        Page<MetaTableAo> metaTablePage = metaTableRepository.searchAll(TENANT_ID.get(),
            pageMetaTableReq.getSearchKeyWord(), pageMetaTableReq.getDatasourceId(),
            PageRequest.of(pageMetaTableReq.getPage(), pageMetaTableReq.getPageSize()));
        JPA_TENANT_MODE.set(true);

        Page<PageMetaTableRes> map = metaTablePage.map(metaMapper::metaTableEntityToPageMetaTableRes);
        map.getContent().forEach(e -> {
            if (!Strings.isEmpty(e.getCustomComment())) {
                e.setTableComment(e.getCustomComment());
            }
            metaDatabaseRepository.findById(e.getDatasourceId()).ifPresent(metaDatabase -> {
                e.setDbName(metaDatabase.getDbName());
                e.setDatasourceName(metaDatabase.getName());
            });
        });
        return map;
    }

    public AddMetaWokRes addMetaWork(AddMetaWokReq addMetaWokReq) {

        // 任务名称不能重复
        if (metaWorkRepository.existsByName(addMetaWokReq.getName())) {
            throw new IsxAppException("采集任务名称重复");
        }

        MetaWorkEntity metaWorkEntity = metaMapper.addMetaWorkToMetaWorkEntity(addMetaWokReq);

        if (addMetaWokReq.getCronConfig() != null) {
            metaWorkEntity.setCronConfig(JSON.toJSONString(addMetaWokReq.getCronConfig()));
        }

        metaWorkEntity.setStatus(MetaWorkStatus.DISABLE);
        MetaWorkEntity save = metaWorkRepository.save(metaWorkEntity);

        return AddMetaWokRes.builder().id(save.getId()).build();
    }

    public void updateMetaWork(UpdateMetaWokReq updateMetaWokReq) {

        MetaWorkEntity metaWork = metaService.getMetaWork(updateMetaWokReq.getId());

        if (MetaWorkStatus.ENABLE.equals(metaWork.getStatus())) {
            throw new IsxAppException("请先禁用再编辑");
        }

        metaWork.setName(updateMetaWokReq.getName());
        metaWork.setRemark(updateMetaWokReq.getRemark());
        metaWork.setDbType(updateMetaWokReq.getDbType());
        metaWork.setDatasourceId(updateMetaWokReq.getDatasourceId());
        metaWork.setCollectType(updateMetaWokReq.getCollectType());
        metaWork.setTablePattern(updateMetaWokReq.getTablePattern());

        if (updateMetaWokReq.getCronConfig() != null) {
            metaWork.setCronConfig(JSON.toJSONString(updateMetaWokReq.getCronConfig()));
        }

        metaWorkRepository.save(metaWork);
    }

    public Page<PageMetaWorkRes> pageMetaWork(PageMetaWorkReq pageMetaWorkReq) {

        Page<MetaWorkEntity> metaWorkPage = metaWorkRepository.searchAll(pageMetaWorkReq.getSearchKeyWord(),
            PageRequest.of(pageMetaWorkReq.getPage(), pageMetaWorkReq.getPageSize()));

        Page<PageMetaWorkRes> result = metaWorkPage.map(metaMapper::metaWorkEntityToPageMetaWorkRes);

        result.getContent().forEach(e -> {
            e.setDatasourceName(datasourceService.getDatasourceName(e.getDatasourceId()));
            if (Strings.isNotEmpty(e.getCronConfigStr())) {
                CronConfig cronConfig = JSON.parseObject(e.getCronConfigStr(), CronConfig.class);
                e.setCronConfig(cronConfig);
                try {
                    CronExpression cronExpression = new CronExpression(cronConfig.getCron());
                    Date nexDateTime = cronExpression.getNextValidTimeAfter(new Date());
                    if (MetaWorkStatus.ENABLE.equals(e.getStatus())) {
                        e.setNextStartTime(DateUtil.toLocalDateTime(nexDateTime));
                    }
                } catch (ParseException parseException) {
                    throw new RuntimeException(parseException);
                }
            }
        });

        return result;
    }

    public Page<PageMetaColumnRes> pageMetaColumn(PageMetaColumnReq pageMetaColumnReq) {

        JPA_TENANT_MODE.set(false);
        Page<MetaColumnAo> metaColumnPage =
            metaColumnRepository.searchAll(TENANT_ID.get(), pageMetaColumnReq.getSearchKeyWord(),
                PageRequest.of(pageMetaColumnReq.getPage(), pageMetaColumnReq.getPageSize()));
        JPA_TENANT_MODE.set(true);

        Page<PageMetaColumnRes> map = metaColumnPage.map(metaMapper::metaColumnEntityToPageMetaColumnRes);
        map.getContent().forEach(e -> {
            if (!Strings.isEmpty(e.getCustomComment())) {
                e.setColumnComment(e.getCustomComment());
            }
            metaDatabaseRepository.findById(e.getDatasourceId()).ifPresent(metaDatabase -> {
                e.setDbName(metaDatabase.getDbName());
                e.setDatasourceName(metaDatabase.getName());
            });
        });
        return map;
    }

    public void triggerMetaWork(TriggerMetaWokReq triggerMetaWokReq) {

        MetaWorkEntity metaWork = metaService.getMetaWork(triggerMetaWokReq.getId());

        MetaInstanceEntity metaInstance =
            MetaInstanceEntity.builder().metaWorkId(metaWork.getId()).status(MetaInstanceStatus.COLLECTING)
                .startDateTime(LocalDateTime.now()).triggerType(MetaTriggerType.FAST_TRIGGER).build();
        metaInstance = metaInstanceRepository.saveAndFlush(metaInstance);

        collectMeta.asyncCollect(metaWork, metaInstance.getId(), USER_ID.get(), TENANT_ID.get());
    }

    public void fastTriggerMetaWork(TriggerMetaWokReq triggerMetaWokReq) {

        MetaWorkEntity metaWork = metaService.getMetaWork(triggerMetaWokReq.getId());

        MetaInstanceEntity metaInstance =
            MetaInstanceEntity.builder().metaWorkId(metaWork.getId()).status(MetaInstanceStatus.COLLECTING)
                .startDateTime(LocalDateTime.now()).triggerType(MetaTriggerType.FAST_TRIGGER).build();
        metaInstance = metaInstanceRepository.saveAndFlush(metaInstance);

        collectMeta.syncCollect(metaWork, metaInstance.getId());

        metaInstance = metaInstanceRepository.findById(metaInstance.getId()).get();
        if (MetaInstanceStatus.FAIL.equals(metaInstance.getStatus())) {
            throw new IsxAppException("采集失败," + metaInstance.getCollectLog());
        }
    }

    public void deleteMetaWork(DeleteMetaWokReq deleteMetaWokReq) {

        MetaWorkEntity metaWork = metaService.getMetaWork(deleteMetaWokReq.getId());

        if (MetaWorkStatus.ENABLE.equals(metaWork.getStatus())) {
            throw new IsxAppException("请先禁用采集任务");
        }

        metaWorkRepository.deleteById(deleteMetaWokReq.getId());
    }

    public Page<PageMetaWorkInstanceRes> pageMetaWorkInstance(PageMetaWorkInstanceReq pageMetaWorkInstanceReq) {

        Page<MetaInstanceEntity> metaInstancePage =
            metaInstanceRepository.searchAll(pageMetaWorkInstanceReq.getSearchKeyWord(),
                PageRequest.of(pageMetaWorkInstanceReq.getPage(), pageMetaWorkInstanceReq.getPageSize()));

        Page<PageMetaWorkInstanceRes> map =
            metaInstancePage.map(metaMapper::metaInstanceEntityToPageMetaWorkInstanceRes);
        map.getContent().forEach(e -> {
            e.setMetaWorkName(metaService.getMetaWorkName(e.getMetaWorkId()));
            if (e.getEndDateTime() != null) {
                e.setDuration(DateUtil.between(DateUtil.date(e.getStartDateTime()), DateUtil.date(e.getEndDateTime()),
                    DateUnit.SECOND));
            }
        });
        return map;
    }

    public void deleteMetaWorkInstance(DeleteMetaWokInstanceReq deleteMetaWokInstanceReq) {

        metaInstanceRepository.deleteById(deleteMetaWokInstanceReq.getId());
    }

    public void abortMetaWorkInstance(AbortMetaWokInstanceReq abortMetaWokInstanceReq) {

        MetaInstanceEntity metaWorkInstance = metaService.getMetaWorkInstance(abortMetaWokInstanceReq.getId());

        if (MetaInstanceStatus.FAIL.equals(metaWorkInstance.getStatus())
            || MetaInstanceStatus.SUCCESS.equals(metaWorkInstance.getStatus())) {
            throw new IsxAppException("运行完的实例无法中止");
        }

        Thread thread = META_WORK_THREAD.get(abortMetaWokInstanceReq.getId());
        if (thread != null) {
            thread.interrupt();
        }

        META_WORK_THREAD.remove(abortMetaWokInstanceReq.getId());
        metaWorkInstance.setStatus(MetaInstanceStatus.ABORT);
        metaWorkInstance.setCollectLog("已中止");
        metaInstanceRepository.save(metaWorkInstance);
    }

    public void enableMetaWork(EnableMetaWokReq enableMetaWokReq) {

        MetaWorkEntity metaWork = metaService.getMetaWork(enableMetaWokReq.getId());

        if (Strings.isEmpty(metaWork.getCronConfig())) {
            throw new IsxAppException("未配置调度信息");
        }

        CronConfig cronConfig = JSON.parseObject(metaWork.getCronConfig(), CronConfig.class);
        if (Strings.isEmpty(cronConfig.getCron())) {
            throw new IsxAppException("定时调度配置异常");
        }

        if (!cronConfig.isEnable()) {
            throw new IsxAppException("未配置调度信息");
        }

        if (MetaWorkStatus.ENABLE.equals(metaWork.getStatus())) {
            throw new IsxAppException("请先下线");
        }

        // 封装quartz请求对象
        JobDataMap jobDataMap = new JobDataMap();
        jobDataMap.put("quartzMetaWork", JSON.toJSONString(QuartzMetaWork.builder().metaWorkId(metaWork.getId())
            .userId(USER_ID.get()).tenantId(TENANT_ID.get()).build()));

        JobDetail jobDetail = JobBuilder.newJob(QuartzMetaJob.class).setJobData(jobDataMap).build();
        Trigger trigger = TriggerBuilder.newTrigger()
            .withSchedule(
                CronScheduleBuilder.cronSchedule(cronConfig.getCron()).withMisfireHandlingInstructionDoNothing())
            .withIdentity(metaWork.getId()).build();

        try {
            scheduler.scheduleJob(jobDetail, trigger);
        } catch (SchedulerException e) {
            log.error(e.getMessage(), e);
            throw new IsxAppException("启动采集任务失败");
        }

        metaWork.setStatus(MetaWorkStatus.ENABLE);
        metaWorkRepository.save(metaWork);
    }

    public void disableMetaWork(DisableMetaWokReq disableMetaWokReq) {

        MetaWorkEntity metaWork = metaService.getMetaWork(disableMetaWokReq.getId());

        if (MetaWorkStatus.DISABLE.equals(metaWork.getStatus())) {
            throw new IsxAppException("已禁用");
        }

        try {
            scheduler.unscheduleJob(TriggerKey.triggerKey(metaWork.getId()));
        } catch (SchedulerException e) {
            log.error(e.getMessage());
            throw new IsxAppException("禁用采集任务失败");
        }

        metaWork.setStatus(MetaWorkStatus.DISABLE);
        metaWorkRepository.save(metaWork);
    }

    public GetMetaTableInfoRes refreshMetaTableInfo(RefreshMetaTableInfoReq refreshMetaTableInfoReq) {

        Optional<MetaTableEntity> table = metaTableRepository.findByDatasourceIdAndTableName(
            refreshMetaTableInfoReq.getDatasourceId(), refreshMetaTableInfoReq.getTableName());
        if (!table.isPresent()) {
            throw new IsxAppException("表不存在，请先采集");
        }

        Optional<MetaTableInfoEntity> byDatasourceIdAndTableName =
            metaTableInfoRepository.findByDatasourceIdAndTableName(refreshMetaTableInfoReq.getDatasourceId(),
                refreshMetaTableInfoReq.getTableName());
        MetaTableInfoEntity tableInfoEntity;
        if (byDatasourceIdAndTableName.isPresent()) {
            tableInfoEntity = byDatasourceIdAndTableName.get();
        } else {
            tableInfoEntity = new MetaTableInfoEntity();
            tableInfoEntity.setDatasourceId(table.get().getDatasourceId());
            tableInfoEntity.setTableName(table.get().getTableName());
        }

        DatasourceEntity datasourceEntity = datasourceService.getDatasource(refreshMetaTableInfoReq.getDatasourceId());
        ConnectInfo connectInfo = datasourceMapper.datasourceEntityToConnectInfo(datasourceEntity);
        Datasource datasource = dataSourceFactory.getDatasource(datasourceEntity.getDbType());
        connectInfo.setDatabase(datasource.parseDbName(datasourceEntity.getJdbcUrl()));
        connectInfo.setTableName(table.get().getTableName());

        // 如果是hive表，需要先分析hive
        if (DatasourceType.HIVE.equals(datasourceEntity.getDbType())) {
            datasource.refreshTableInfo(connectInfo);
        }

        tableInfoEntity.setColumnCount(datasource.getTableColumnCount(connectInfo));
        tableInfoEntity.setTotalRows(datasource.getTableTotalRows(connectInfo));
        tableInfoEntity.setTotalSize(datasource.getTableTotalSize(connectInfo));
        tableInfoEntity.setLastModifiedDateTime(LocalDateTime.now());

        metaTableInfoRepository.save(tableInfoEntity);

        return getMetaTableInfo(GetMetaTableInfoReq.builder().datasourceId(table.get().getDatasourceId())
            .tableName(table.get().getTableName()).build());
    }

    public GetMetaTableInfoRes getMetaTableInfo(GetMetaTableInfoReq getMetaTableInfoReq) {

        Optional<MetaTableEntity> table = metaTableRepository
            .findByDatasourceIdAndTableName(getMetaTableInfoReq.getDatasourceId(), getMetaTableInfoReq.getTableName());
        if (!table.isPresent()) {
            throw new IsxAppException("表不存在，请先采集");
        }

        GetMetaTableInfoRes getMetaTableInfoRes;
        Optional<MetaTableInfoEntity> byDatasourceIdAndTableName = metaTableInfoRepository
            .findByDatasourceIdAndTableName(getMetaTableInfoReq.getDatasourceId(), getMetaTableInfoReq.getTableName());
        if (byDatasourceIdAndTableName.isPresent()) {
            getMetaTableInfoRes = metaMapper.metaTableInfoEntityAndmetaTableEntityToGetMetaTableInfoRes(
                byDatasourceIdAndTableName.get(), table.get());
        } else {
            getMetaTableInfoRes = metaMapper.metaTableEntityToGetMetaTableInfoRes(table.get());
        }

        if (getMetaTableInfoRes.getTotalSize() != null) {
            getMetaTableInfoRes.setTotalSizeStr(DataSizeUtil.format(getMetaTableInfoRes.getTotalSize()));
        }
        return getMetaTableInfoRes;
    }

    public List<QueryColumnDto> getMetaTableColumn(GetMetaTableColumnReq getMetaTableColumnReq) {

        Optional<MetaTableEntity> table = metaTableRepository.findByDatasourceIdAndTableName(
            getMetaTableColumnReq.getDatasourceId(), getMetaTableColumnReq.getTableName());
        if (!table.isPresent()) {
            throw new IsxAppException("表不存在，请先采集");
        }

        JPA_TENANT_MODE.set(false);
        List<MetaColumnAo> metaColumnEntities = metaColumnRepository.queryAllByDatasourceIdAndTableName(TENANT_ID.get(),
            getMetaTableColumnReq.getDatasourceId(), getMetaTableColumnReq.getTableName());
        JPA_TENANT_MODE.set(true);

        List<QueryColumnDto> queryColumnDtos = metaMapper.metaColumnEntitiesToQueryColumnDtoList(metaColumnEntities);
        queryColumnDtos.forEach(e -> {
            if (!Strings.isEmpty(e.getCustomComment())) {
                e.setColumnComment(e.getCustomComment());
            }
        });

        return queryColumnDtos;
    }

    public GetDataSourceDataRes getMetaTableData(GetMetaTableDataReq getMetaTableDataReq) {

        Optional<MetaTableEntity> table = metaTableRepository
            .findByDatasourceIdAndTableName(getMetaTableDataReq.getDatasourceId(), getMetaTableDataReq.getTableName());
        if (!table.isPresent()) {
            throw new IsxAppException("表不存在，请先采集");
        }

        DatasourceEntity datasourceEntity = datasourceService.getDatasource(table.get().getDatasourceId());
        ConnectInfo connectInfo = datasourceMapper.datasourceEntityToConnectInfo(datasourceEntity);
        Datasource datasource = dataSourceFactory.getDatasource(connectInfo.getDbType());

        connectInfo.setTableName(getMetaTableDataReq.getTableName());
        connectInfo.setDatabase(datasource.parseDbName(datasourceEntity.getJdbcUrl()));
        connectInfo.setRowNumber("200");
        return datasource.getTableData(connectInfo);
    }

    public void exportTableExcel(ExportTableExcelReq exportTableExcelReq, HttpServletResponse response) {

        Optional<MetaTableEntity> table = metaTableRepository
            .findByDatasourceIdAndTableName(exportTableExcelReq.getDatasourceId(), exportTableExcelReq.getTableName());
        if (!table.isPresent()) {
            throw new IsxAppException("表不存在，请先采集");
        }

        DatasourceEntity datasourceEntity = datasourceService.getDatasource(table.get().getDatasourceId());
        ConnectInfo connectInfo = datasourceMapper.datasourceEntityToConnectInfo(datasourceEntity);
        Datasource datasource = dataSourceFactory.getDatasource(connectInfo.getDbType());

        // 如果超过1万条数据，暂不支持
        connectInfo.setDatabase(datasource.parseDbName(datasourceEntity.getJdbcUrl()));
        connectInfo.setTableName(table.get().getTableName());
        Long tableTotalRows = datasource.getTableTotalRows(connectInfo);
        if (tableTotalRows > 10000) {
            throw new IsxAppException("暂不支持10000条数据导出");
        }

        GetDataSourceDataRes allData;
        connectInfo.setRowNumber("ALL");
        allData = datasource.getTableData(connectInfo);

        try {
            ExcelWriter writer = ExcelUtil.getWriter();
            writer.writeHeadRow(allData.getColumns());
            allData.getRows().forEach(writer::writeRow);

            response.setContentType("application/vnd.ms-excel;charset=utf-8");
            response.setHeader("Content-disposition",
                "attachment; filename=" + URLEncoder.encode(table.get().getTableName() + ".xls", "UTF-8"));

            writer.flush(response.getOutputStream(), true);
            writer.close();

        } catch (IOException e) {
            log.debug(e.getMessage(), e);
            throw new IsxAppException("读取文件失败");
        }
    }

    public void updateDatabaseComment(UpdateDatabaseCommentReq updateDatabaseCommentReq) {

        Optional<MetaDatabaseEntity> datasourceIdOption =
            metaDatabaseRepository.findByDatasourceId(updateDatabaseCommentReq.getDatasourceId());
        if (datasourceIdOption.isPresent()) {
            MetaDatabaseEntity metaDatabaseEntity = datasourceIdOption.get();
            metaDatabaseEntity.setDbComment(updateDatabaseCommentReq.getComment());
            metaDatabaseRepository.save(metaDatabaseEntity);
        }
    }

    public void updateTableComment(UpdateTableCommentReq updateTableCommentReq) {

        Optional<MetaTableInfoEntity> byDatasourceIdAndTableName =
            metaTableInfoRepository.findByDatasourceIdAndTableName(updateTableCommentReq.getDatasourceId(),
                updateTableCommentReq.getTableName());
        MetaTableInfoEntity metaTableInfoEntity;
        if (byDatasourceIdAndTableName.isPresent()) {
            metaTableInfoEntity = byDatasourceIdAndTableName.get();
        } else {
            metaTableInfoEntity = new MetaTableInfoEntity();
            metaTableInfoEntity.setDatasourceId(updateTableCommentReq.getDatasourceId());
            metaTableInfoEntity.setTableName(updateTableCommentReq.getTableName());
        }
        metaTableInfoEntity.setCustomComment(updateTableCommentReq.getComment());
        metaTableInfoRepository.save(metaTableInfoEntity);
    }

    public void updateColumnComment(UpdateColumnCommentReq updateColumnCommentReq) {

        Optional<MetaColumnInfoEntity> byDatasourceIdAndTableNameAndColumnName = metaColumnInfoRepository
            .findByDatasourceIdAndTableNameAndColumnName(updateColumnCommentReq.getDatasourceId(),
                updateColumnCommentReq.getTableName(), updateColumnCommentReq.getColumnName());
        MetaColumnInfoEntity metaColumnInfoEntity;
        if (byDatasourceIdAndTableNameAndColumnName.isPresent()) {
            metaColumnInfoEntity = byDatasourceIdAndTableNameAndColumnName.get();
        } else {
            metaColumnInfoEntity = new MetaColumnInfoEntity();
            metaColumnInfoEntity.setDatasourceId(updateColumnCommentReq.getDatasourceId());
            metaColumnInfoEntity.setTableName(updateColumnCommentReq.getTableName());
            metaColumnInfoEntity.setColumnName(updateColumnCommentReq.getColumnName());
        }
        metaColumnInfoEntity.setCustomComment(updateColumnCommentReq.getComment());
        metaColumnInfoRepository.save(metaColumnInfoEntity);
    }
}
