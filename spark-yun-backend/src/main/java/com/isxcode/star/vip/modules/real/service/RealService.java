package com.isxcode.star.vip.modules.real.service;

import com.isxcode.star.backend.api.base.exceptions.IsxAppException;
import com.isxcode.star.modules.real.entity.RealEntity;
import com.isxcode.star.modules.real.repository.RealRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.transaction.Transactional;

@Service
@Slf4j
@RequiredArgsConstructor
@Transactional
public class RealService {

    private final RealRepository realRepository;

    public RealEntity getReal(String realId) {

        return realRepository.findById(realId).orElseThrow(() -> new IsxAppException("实时作业不存在"));
    }
}
