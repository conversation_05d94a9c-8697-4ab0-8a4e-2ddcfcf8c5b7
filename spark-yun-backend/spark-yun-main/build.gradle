plugins {
    id 'com.github.johnrengelman.shadow' version '7.1.2'
}

dependencies {

    api(project(':spark-yun-backend:spark-yun-modules'))
    api(project(':spark-yun-vip:spark-yun-backend'))

    implementation 'org.springframework.boot:spring-boot-starter-thymeleaf'

    implementation "com.alibaba:druid-spring-boot-starter:${DRUID_VERSION}"

    implementation "org.flywaydb:flyway-core:${FLYWAY_VERSION}"
    implementation "org.flywaydb:flyway-mysql:${FLYWAY_VERSION}"
}

bootJar {
    archiveFileName = 'zhiqingyun.jar'
}

shadowJar {
    archiveClassifier = ''
    archiveFileName = 'zhiqingyun.jar'
    zip64 = true
    mergeServiceFiles()
    manifest {
        attributes 'Main-Class': 'com.isxcode.star.SparkYunApplication'
    }
}

bootRun {
    workingDir(rootDir.getAbsolutePath())
}