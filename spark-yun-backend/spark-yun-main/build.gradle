dependencies {

    api(project(':spark-yun-backend:spark-yun-modules'))
    api(project(':spark-yun-vip:spark-yun-backend'))

    implementation 'org.springframework.boot:spring-boot-starter-thymeleaf'

    implementation "com.alibaba:druid-spring-boot-starter:${DRUID_VERSION}"

    implementation "org.flywaydb:flyway-core:${FLYWAY_VERSION}"
    implementation "org.flywaydb:flyway-mysql:${FLYWAY_VERSION}"
}

bootJar {
    archiveFileName = 'zhiqingyun.jar'
}

bootRun {
    workingDir(rootDir.getAbsolutePath())
}